#!/usr/bin/env deno run --allow-net --allow-read --allow-env

/**
 * Site Settings Collection Creation Script
 *
 * This script creates the site_settings collection in PocketBase
 * and populates it with default data.
 */

import { load } from "https://deno.land/std@0.208.0/dotenv/mod.ts";
import PocketBase from 'https://deno.land/x/pocketbase@0.19.0/mod.ts';

// Load environment variables
const env = await load();

// Configuration
const POCKETBASE_URL = Deno.env.get('POCKETBASE_URL') || env.POCKETBASE_URL || 'http://localhost:8090';
const ADMIN_EMAIL = Deno.env.get('POCKETBASE_EMAIL') || env.POCKETBASE_EMAIL || '<EMAIL>';
const ADMIN_PASSWORD = Deno.env.get('POCKETBASE_PASSWORD') || env.POCKETBASE_PASSWORD || 'admin123456';

interface PocketBaseResponse {
  id?: string;
  message?: string;
  code?: number;
  data?: any;
}

class PocketBaseClient {
  private baseUrl: string;
  private authToken: string = '';

  constructor(baseUrl: string) {
    this.baseUrl = baseUrl.replace(/\/$/, '');
  }

  async authenticate(email: string, password: string): Promise<void> {
    const response = await fetch(`${this.baseUrl}/api/admins/auth-via-email`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: email,
        password: password,
      }),
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Authentication failed: ${error}`);
    }

    const data = await response.json();
    this.authToken = data.token;
    console.log('✅ Authenticated successfully');
  }

  async createCollection(schema: any): Promise<PocketBaseResponse> {
    const response = await fetch(`${this.baseUrl}/api/collections`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.authToken}`,
      },
      body: JSON.stringify(schema),
    });

    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(`Failed to create collection: ${JSON.stringify(data)}`);
    }

    return data;
  }

  async getCollection(name: string): Promise<PocketBaseResponse | null> {
    try {
      const response = await fetch(`${this.baseUrl}/api/collections/${name}`, {
        headers: {
          'Authorization': `Bearer ${this.authToken}`,
        },
      });

      if (response.status === 404) {
        return null;
      }

      if (!response.ok) {
        throw new Error(`Failed to get collection: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      return null;
    }
  }

  async createRecord(collection: string, data: any): Promise<PocketBaseResponse> {
    const response = await fetch(`${this.baseUrl}/api/collections/${collection}/records`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.authToken}`,
      },
      body: JSON.stringify(data),
    });

    const result = await response.json();
    
    if (!response.ok) {
      throw new Error(`Failed to create record: ${JSON.stringify(result)}`);
    }

    return result;
  }

  async getRecords(collection: string): Promise<PocketBaseResponse> {
    const response = await fetch(`${this.baseUrl}/api/collections/${collection}/records`, {
      headers: {
        'Authorization': `Bearer ${this.authToken}`,
      },
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Failed to get records: ${error}`);
    }

    return await response.json();
  }
}

async function loadSchema(): Promise<any> {
  try {
    const schemaText = await Deno.readTextFile('./schemas/site_settings.json');
    return JSON.parse(schemaText);
  } catch (error) {
    throw new Error(`Failed to load schema: ${error.message}`);
  }
}

async function loadDefaultData(): Promise<any> {
  try {
    const dataText = await Deno.readTextFile('./data/default-site-settings.json');
    return JSON.parse(dataText);
  } catch (error) {
    throw new Error(`Failed to load default data: ${error.message}`);
  }
}

async function main() {
  console.log('🚀 Starting Site Settings Collection Creation...');
  console.log(`📡 PocketBase URL: ${POCKETBASE_URL}`);

  try {
    // Initialize PocketBase client
    const pb = new PocketBaseClient(POCKETBASE_URL);

    // Authenticate
    console.log('🔐 Authenticating...');
    await pb.authenticate(ADMIN_EMAIL, ADMIN_PASSWORD);

    // Check if collection already exists
    console.log('🔍 Checking if site_settings collection exists...');
    const existingCollection = await pb.getCollection('site_settings');
    
    if (existingCollection) {
      console.log('⚠️  Collection site_settings already exists');
      
      // Check if it has records
      const records = await pb.getRecords('site_settings');
      console.log(`📊 Found ${records.items?.length || 0} existing records`);
      
      if (records.items?.length === 0) {
        console.log('📝 Creating default record...');
        const defaultData = await loadDefaultData();
        const record = await pb.createRecord('site_settings', defaultData);
        console.log(`✅ Default record created with ID: ${record.id}`);
      } else {
        console.log('✅ Collection already has data');
      }
      
      return;
    }

    // Load schema
    console.log('📋 Loading schema...');
    const schema = await loadSchema();

    // Create collection
    console.log('🏗️  Creating site_settings collection...');
    const collection = await pb.createCollection(schema);
    console.log(`✅ Collection created with ID: ${collection.id}`);

    // Load default data
    console.log('📄 Loading default data...');
    const defaultData = await loadDefaultData();

    // Create default record
    console.log('📝 Creating default record...');
    const record = await pb.createRecord('site_settings', defaultData);
    console.log(`✅ Default record created with ID: ${record.id}`);

    // Verify creation
    console.log('🔍 Verifying collection...');
    const verification = await pb.getRecords('site_settings');
    console.log(`✅ Verification complete: ${verification.items?.length || 0} records found`);

    console.log('🎉 Site Settings collection setup completed successfully!');

  } catch (error) {
    console.error('❌ Error:', error.message);
    Deno.exit(1);
  }
}

// Run the script
if (import.meta.main) {
  await main();
}
