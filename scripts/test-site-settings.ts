#!/usr/bin/env deno run --allow-net --allow-read --allow-env

/**
 * Site Settings Collection Test Script
 * 
 * This script tests the site_settings collection functionality
 * including CRUD operations and validation rules.
 */

import { load } from "https://deno.land/std@0.208.0/dotenv/mod.ts";

// Load environment variables
const env = await load();

// Configuration
const POCKETBASE_URL = Deno.env.get('POCKETBASE_URL') || env.POCKETBASE_URL || 'http://localhost:8090';
const ADMIN_EMAIL = Deno.env.get('POCKETBASE_EMAIL') || env.POCKETBASE_EMAIL || '<EMAIL>';
const ADMIN_PASSWORD = Deno.env.get('POCKETBASE_PASSWORD') || env.POCKETBASE_PASSWORD || 'admin123456';

class PocketBaseClient {
  private baseUrl: string;
  private authToken: string = '';

  constructor(baseUrl: string) {
    this.baseUrl = baseUrl.replace(/\/$/, '');
  }

  async authenticate(email: string, password: string): Promise<void> {
    const response = await fetch(`${this.baseUrl}/api/admins/auth-with-password`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        identity: email,
        password: password,
      }),
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Authentication failed: ${error}`);
    }

    const data = await response.json();
    this.authToken = data.token;
  }

  async getRecords(collection: string): Promise<any> {
    const response = await fetch(`${this.baseUrl}/api/collections/${collection}/records`, {
      headers: {
        'Authorization': `Bearer ${this.authToken}`,
      },
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Failed to get records: ${error}`);
    }

    return await response.json();
  }

  async updateRecord(collection: string, id: string, data: any): Promise<any> {
    const response = await fetch(`${this.baseUrl}/api/collections/${collection}/records/${id}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.authToken}`,
      },
      body: JSON.stringify(data),
    });

    const result = await response.json();
    
    if (!response.ok) {
      throw new Error(`Failed to update record: ${JSON.stringify(result)}`);
    }

    return result;
  }

  async getRecord(collection: string, id: string): Promise<any> {
    const response = await fetch(`${this.baseUrl}/api/collections/${collection}/records/${id}`, {
      headers: {
        'Authorization': `Bearer ${this.authToken}`,
      },
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Failed to get record: ${error}`);
    }

    return await response.json();
  }
}

async function testValidation(pb: PocketBaseClient, recordId: string) {
  console.log('🧪 Testing field validation...');

  const tests = [
    {
      name: 'Valid email update',
      data: { contact_email: '<EMAIL>' },
      shouldPass: true
    },
    {
      name: 'Invalid email format',
      data: { contact_email: 'invalid-email' },
      shouldPass: false
    },
    {
      name: 'Company name too long',
      data: { company_name: 'A'.repeat(101) },
      shouldPass: false
    },
    {
      name: 'Valid URL',
      data: { social_facebook: 'https://facebook.com/ptbl' },
      shouldPass: true
    },
    {
      name: 'Invalid URL',
      data: { social_facebook: 'not-a-url' },
      shouldPass: false
    },
    {
      name: 'Valid maintenance mode',
      data: { maintenance_mode: true },
      shouldPass: true
    }
  ];

  for (const test of tests) {
    try {
      await pb.updateRecord('site_settings', recordId, test.data);
      if (test.shouldPass) {
        console.log(`  ✅ ${test.name}: PASSED`);
      } else {
        console.log(`  ❌ ${test.name}: FAILED (should have been rejected)`);
      }
    } catch (error) {
      if (!test.shouldPass) {
        console.log(`  ✅ ${test.name}: PASSED (correctly rejected)`);
      } else {
        console.log(`  ❌ ${test.name}: FAILED (${error.message})`);
      }
    }
  }
}

async function testCRUDOperations(pb: PocketBaseClient) {
  console.log('🔧 Testing CRUD operations...');

  try {
    // Read operation
    console.log('  📖 Testing READ operation...');
    const records = await pb.getRecords('site_settings');
    console.log(`  ✅ READ: Found ${records.items?.length || 0} records`);

    if (records.items && records.items.length > 0) {
      const record = records.items[0];
      console.log(`  📄 Record ID: ${record.id}`);
      console.log(`  🏢 Company: ${record.company_name}`);
      console.log(`  📧 Email: ${record.contact_email}`);

      // Update operation
      console.log('  ✏️  Testing UPDATE operation...');
      const updateData = {
        company_tagline: 'Updated tagline for testing',
        meta_title: 'Updated Meta Title'
      };
      
      const updatedRecord = await pb.updateRecord('site_settings', record.id, updateData);
      console.log('  ✅ UPDATE: Record updated successfully');
      console.log(`  🏷️  New tagline: ${updatedRecord.company_tagline}`);

      // Verify update
      const verifyRecord = await pb.getRecord('site_settings', record.id);
      if (verifyRecord.company_tagline === updateData.company_tagline) {
        console.log('  ✅ UPDATE VERIFICATION: Changes persisted correctly');
      } else {
        console.log('  ❌ UPDATE VERIFICATION: Changes not persisted');
      }

      // Test validation
      await testValidation(pb, record.id);

      // Restore original data
      console.log('  🔄 Restoring original data...');
      await pb.updateRecord('site_settings', record.id, {
        company_tagline: record.company_tagline,
        meta_title: record.meta_title
      });
      console.log('  ✅ Original data restored');

    } else {
      console.log('  ⚠️  No records found to test UPDATE operation');
    }

  } catch (error) {
    console.log(`  ❌ CRUD Test failed: ${error.message}`);
  }
}

async function testAPIAccess(pb: PocketBaseClient) {
  console.log('🔐 Testing API access rules...');

  try {
    // Test authenticated access
    const records = await pb.getRecords('site_settings');
    console.log('  ✅ Authenticated access: ALLOWED');
    
    // Note: Testing unauthenticated access would require a separate client
    // without authentication, which is beyond the scope of this basic test
    console.log('  ℹ️  Unauthenticated access test skipped (requires separate client)');

  } catch (error) {
    console.log(`  ❌ API Access test failed: ${error.message}`);
  }
}

async function main() {
  console.log('🧪 Starting Site Settings Collection Tests...');
  console.log(`📡 PocketBase URL: ${POCKETBASE_URL}`);

  try {
    // Initialize PocketBase client
    const pb = new PocketBaseClient(POCKETBASE_URL);

    // Authenticate
    console.log('🔐 Authenticating...');
    await pb.authenticate(ADMIN_EMAIL, ADMIN_PASSWORD);
    console.log('✅ Authentication successful');

    // Test CRUD operations
    await testCRUDOperations(pb);

    // Test API access rules
    await testAPIAccess(pb);

    console.log('🎉 All tests completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    Deno.exit(1);
  }
}

// Run the tests
if (import.meta.main) {
  await main();
}
