#!/usr/bin/env deno run --allow-net --allow-read --allow-env

/**
 * Simple Site Settings Collection Creation Script
 * 
 * This script creates the site_settings collection in PocketBase
 * using the PocketBase JavaScript SDK.
 */

import PocketBase from 'https://deno.land/x/pocketbase@0.19.0/mod.ts';
import { load } from "https://deno.land/std@0.208.0/dotenv/mod.ts";

// Load environment variables
const env = await load();

// Configuration
const POCKETBASE_URL = Deno.env.get('POCKETBASE_URL') || env.POCKETBASE_URL || 'http://localhost:8090';
const ADMIN_EMAIL = Deno.env.get('POCKETBASE_EMAIL') || env.POCKETBASE_EMAIL || '<EMAIL>';
const ADMIN_PASSWORD = Deno.env.get('POCKETBASE_PASSWORD') || env.POCKETBASE_PASSWORD || 'admin123456';

async function loadSchema(): Promise<any> {
  try {
    const schemaText = await Deno.readTextFile('./schemas/site_settings.json');
    return JSON.parse(schemaText);
  } catch (error) {
    throw new Error(`Failed to load schema: ${(error as Error).message}`);
  }
}

async function loadDefaultData(): Promise<any> {
  try {
    const dataText = await Deno.readTextFile('./data/default-site-settings.json');
    return JSON.parse(dataText);
  } catch (error) {
    throw new Error(`Failed to load default data: ${(error as Error).message}`);
  }
}

async function main() {
  console.log('🚀 Starting Site Settings Collection Creation...');
  console.log(`📡 PocketBase URL: ${POCKETBASE_URL}`);

  try {
    // Initialize PocketBase client
    const pb = new PocketBase(POCKETBASE_URL);

    // Authenticate as admin
    console.log('🔐 Authenticating...');
    await pb.admins.authWithPassword(ADMIN_EMAIL, ADMIN_PASSWORD);
    console.log('✅ Authenticated successfully');

    // Check if collection already exists
    console.log('🔍 Checking if site_settings collection exists...');
    try {
      const existingCollection = await pb.collections.getOne('site_settings');
      console.log('⚠️  Collection site_settings already exists');
      
      // Check if it has records
      const records = await pb.collection('site_settings').getFullList();
      console.log(`📊 Found ${records.length} existing records`);
      
      if (records.length === 0) {
        console.log('📝 Creating default record...');
        const defaultData = await loadDefaultData();
        const record = await pb.collection('site_settings').create(defaultData);
        console.log(`✅ Default record created with ID: ${record.id}`);
      } else {
        console.log('✅ Collection already has data');
      }
      
      return;
    } catch (error) {
      // Collection doesn't exist, continue with creation
      console.log('📋 Collection does not exist, creating...');
    }

    // Load schema
    console.log('📋 Loading schema...');
    const schema = await loadSchema();

    // Create collection
    console.log('🏗️  Creating site_settings collection...');
    const collection = await pb.collections.create(schema);
    console.log(`✅ Collection created with ID: ${collection.id}`);

    // Load default data
    console.log('📄 Loading default data...');
    const defaultData = await loadDefaultData();

    // Create default record
    console.log('📝 Creating default record...');
    const record = await pb.collection('site_settings').create(defaultData);
    console.log(`✅ Default record created with ID: ${record.id}`);

    // Verify creation
    console.log('🔍 Verifying collection...');
    const verification = await pb.collection('site_settings').getFullList();
    console.log(`✅ Verification complete: ${verification.length} records found`);

    console.log('🎉 Site Settings collection setup completed successfully!');

  } catch (error) {
    console.error('❌ Error:', (error as Error).message);
    console.error('Full error:', error);
    Deno.exit(1);
  }
}

// Run the script
if (import.meta.main) {
  await main();
}
