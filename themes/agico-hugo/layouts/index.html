{{ define "main" }}

{{"<!-- banner -->"| safeHTML}}
{{ with .Params.banner }}
{{ if .enable }}
<section class="hero-area bg-cover" data-background="{{ .bg_image | absURL }}">
  <div class="container">
    <div class="row align-items-center justify-content-between">
      <div class="col-lg-{{ if .image }}6{{ else }}12{{ end }} pl-lg-0 text-center text-lg-left">
        <h1 class="text-white position-relative" data-aos="fade-up" data-aos-delay="1000">{{ .title }}<span class="watermark">{{ .watermark }}</span></h1>
        <p class="text-white pt-2 pb-3" data-aos="fade-up" data-aos-delay="1200">{{ .content | markdownify }}</p>
        {{ if .button.enable }}
        {{ with .button }}
        <a href="{{ .link | absURL  }}" class="btn btn-primary" data-aos="fade-up" data-aos-delay="1400">{{ .label }}</a>
        {{ end }}
        {{ end }}
      </div>
      <!-- {{ if .image }}
      <div class="col-lg-5 pl-lg-0 pt-5 pt-lg-0 text-lg-right text-center">
        <img src="{{ .image | absURL }}" class="img-fluid" alt="illustration" data-aos="zoom-in" data-aos-delay="1500">
      </div>
      {{ end }} -->
    </div>
  </div>
</section>
{{ end }}
{{ end }}
{{"<!-- /banner -->"| safeHTML}}

{{"<!-- partner -->"| safeHTML}}
{{ with .Params.partner }}
{{ if .enable }}
<section class="partner-section">
  <div class="container">
    <div class="row">
      <div class="col-12">
        <div class="partner-slider">
          {{ range .partner_logo }}
          <div class="partner-item">
            <div class="partner-card">
              <img src="{{ . | absURL }}" alt="partner" class="partner-logo">
            </div>
          </div>
          {{ end }}
        </div>
      </div>
    </div>
  </div>
</section>
{{ end }}
{{ end }}
{{"<!-- /partner -->"| safeHTML}}

<style>
.partner-section {
  background: linear-gradient(to right, #f8f9fa, #ffffff, #f8f9fa);
  border-bottom: 1px solid rgba(0,0,0,0.05);
}

.partner-slider {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 30px;
}

.partner-item {
  flex: 0 1 200px;
}

.partner-card {
  background: white;
  padding: 25px;
  border-radius: 15px;
  box-shadow: 0 5px 20px rgba(0,0,0,0.03);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 120px;
}

.partner-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.08);
}

.partner-logo {
  max-width: 100%;
  max-height: 70px;
  object-fit: contain;
  transition: all 0.3s ease;
}

.partner-card:hover .partner-logo {
  filter: grayscale(100%);
}

@media (max-width: 768px) {
  .partner-item {
    flex: 0 1 150px;
  }
  
  .partner-card {
    padding: 15px;
    height: 100px;
  }
  
  .partner-logo {
    max-height: 50px;
  }
}
</style>

{{"<!-- feature -->"| safeHTML}}
{{ with .Params.feature }}
{{ if .enable }}
<section class="section pb-0">
  <div class="container">
    <div class="row">
      <div class="col-lg-12 text-center">
        <p class="subtitle" data-aos="fade-up">{{ .subtitle }}</p>
        <h2 class="section-title" data-aos="fade-up" data-aos-delay="100">{{ .title }}</h2>
      </div>
      <div class="col-12">
        <div class="feature-carousel">
          <button class="nav-arrow nav-prev" aria-label="Previous slide">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M15 18l-6-6 6-6"/>
            </svg>
          </button>
          <div class="feature-track">
            {{ range $index, $element:= .feature_item }}
            {{ $delay := mul $index 200 }}
            <div class="feature-slide" data-aos="fade-up" data-aos-delay="{{ $delay }}">
              <div class="hover-bg-primary text-center position-relative rounded-lg shadow overflow-hidden" style="height: 450px;">
                <img src="{{ .image | absURL }}" class="img-fluid w-100" alt="feature-image" style="height: 200px; object-fit: cover;">
                <div class="px-4 py-5" style="height: 250px; overflow-y: auto;">
                  <h5 class="pb-3 card-title">{{ .name }}</h5>
                  <p class="mb-4">{{ .content | markdownify }}</p>
                </div>
              </div>
            </div>
            {{ end }}
          </div>
          <button class="nav-arrow nav-next" aria-label="Next slide">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M9 18l6-6-6-6"/>
            </svg>
          </button>
          <div class="nav-dots"></div>
        </div>
      </div>
    </div>
  </div>
</section>
{{ end }}
{{ end }}
{{"<!-- feature -->"| safeHTML}}

<style>
.feature-carousel {
  position: relative;
  overflow: hidden;
}

.feature-track {
  display: flex;
  flex-wrap: wrap;
}

@media (max-width: 768px) {
  .feature-carousel {
    padding: 0;
    margin: 0 -15px;
  }

  .feature-track {
    flex-wrap: nowrap;
    transition: transform 0.3s ease-in-out;
  }

  .feature-slide {
    flex: 0 0 100%;
    padding: 0 15px;
  }

  .nav-arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
    background: rgba(255, 255, 255, 0.9);
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    color: #333;
    transition: all 0.3s ease;
  }

  .nav-arrow:hover {
    background: white;
    box-shadow: 0 3px 8px rgba(0,0,0,0.15);
  }

  .nav-prev {
    left: 10px;
  }

  .nav-next {
    right: 10px;
  }

  .nav-dots {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-top: 20px;
    padding: 10px 0;
  }

  .nav-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #ddd;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .nav-dot.active {
    width: 8px;
    height: 8px;
    background: #007bff;
  }
}

@media (min-width: 769px) {
  .feature-track {
    margin: 0 -15px;
  }
  
  .feature-slide {
    flex: 0 0 33.333%;
    padding: 15px;
  }

  .nav-arrow, .nav-dots {
    display: none;
  }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const carousel = document.querySelector('.feature-carousel');
  if (!carousel || window.innerWidth > 768) return;

  const track = carousel.querySelector('.feature-track');
  const slides = carousel.querySelectorAll('.feature-slide');
  const navDots = carousel.querySelector('.nav-dots');
  const prevBtn = carousel.querySelector('.nav-prev');
  const nextBtn = carousel.querySelector('.nav-next');
  
  let currentSlide = 0;
  
  // Create dots
  slides.forEach((_, i) => {
    const dot = document.createElement('div');
    dot.className = `nav-dot ${i === 0 ? 'active' : ''}`;
    dot.addEventListener('click', () => goToSlide(i));
    navDots.appendChild(dot);
  });
  
  function updateDots() {
    const dots = navDots.querySelectorAll('.nav-dot');
    dots.forEach((dot, i) => {
      dot.classList.toggle('active', i === currentSlide);
    });
  }
  
  function goToSlide(n) {
    currentSlide = n;
    track.style.transform = `translateX(-${currentSlide * 100}%)`;
    updateDots();
  }
  
  prevBtn.addEventListener('click', () => {
    currentSlide = (currentSlide - 1 + slides.length) % slides.length;
    goToSlide(currentSlide);
  });
  
  nextBtn.addEventListener('click', () => {
    currentSlide = (currentSlide + 1) % slides.length;
    goToSlide(currentSlide);
  });
  
  // Touch support
  let touchStartX = 0;
  let touchEndX = 0;
  
  track.addEventListener('touchstart', e => {
    touchStartX = e.changedTouches[0].screenX;
  });
  
  track.addEventListener('touchend', e => {
    touchEndX = e.changedTouches[0].screenX;
    if (touchStartX - touchEndX > 50) {
      nextBtn.click();
    } else if (touchEndX - touchStartX > 50) {
      prevBtn.click();
    }
  });
});
</script>

{{"<!-- about -->"| safeHTML}}
{{ with .Params.about }}
{{ if .enable }}
{{ range $i,$p := .about_item }}
<section class="section pb-0">
  <div class="container">
    <div class="row align-items-center">
      {{ if not (modBool $i 2)}}
      <div class="col-lg-6 col-md-5 text-center text-md-left mb-4 mb-md-0">
        <div class="about-image-wrapper" data-aos="fade-right">
          <img src="{{ .image | absURL}}" class="img-fluid about-image" alt="about-image">
        </div>
      </div>
      <div class="col-lg-6 col-md-7 text-center text-md-left">
        <p class="subtitle" data-aos="fade-left">{{ .subtitle }}</p>
        <h2 class="section-title" data-aos="fade-left" data-aos-delay="200">{{ .title }}</h2>
        <p class="mb-4" data-aos="fade-left" data-aos-delay="300">{{ .content | markdownify }}</p>
        {{ if .button.enable }}
        {{ with .button }}
        <a href="{{ .link | absLangURL }}" class="btn btn-outline-primary" data-aos="fade-left" data-aos-delay="500">{{ .label }}</a>
        {{ end }}
        {{ end }}
      </div>
      {{ else }}
      <div class="col-lg-6 col-md-7 text-center text-md-left order-md-1 order-2">
        <p class="subtitle" data-aos="fade-right">{{ .subtitle }}</p>
        <h2 class="section-title" data-aos="fade-right" data-aos-delay="200">{{ .title }}</h2>
        <p class="mb-4" data-aos="fade-right" data-aos-delay="300">{{ .content | markdownify }}</p>
        {{ if .button.enable }}
        {{ with .button }}
        <a href="{{ .link | absLangURL }}" class="btn btn-outline-primary" data-aos="fade-right" data-aos-delay="500">{{ .label }}</a>
        {{ end }}
        {{ end }}
      </div>
      <div class="col-lg-6 col-md-5 text-center text-md-left order-1 order-md-2 mb-4 mb-md-0">
        <div class="about-image-wrapper" data-aos="fade-left">
          <img src="{{ .image | absURL}}" class="img-fluid about-image" alt="about-image">
        </div>
      </div>
      {{ end }}
    </div>
  </div>
</section>
{{ end }}
{{ end }}
{{ end }}
{{"<!-- about -->"| safeHTML}}

<style>
.about-image-wrapper {
  position: relative;
  padding: 15px;
}

.about-image {
  border-radius: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.about-image:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.about-image-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 35px;
  background: linear-gradient(45deg, 
    {{ site.Params.variables.primary_color | default "#2962ff" }}22,
    {{ site.Params.variables.secondary_color | default "#0d47a1" }}11
  );
  transform: rotate(-2deg);
  z-index: -1;
}
</style>

{{"<!-- promo image section -->"| safeHTML}}
{{ with .Params.promo_video }}
{{ if .enable }}
<section class="section-lg pb-0 bg-cover" data-background="{{`images/background/check-video.png` | absURL }}">
  <div class="container">
    <div class="row align-items-center">
      <div class="col-md-6 pr-lg-5 mb-md-0 mb-5">
        <div class="position-relative rounded-lg" data-aos="zoom-in">
          <img src="{{ .video_thumbnail | absURL }}" alt="{{ .title }}" class="img-fluid rounded-lg w-100">
        </div>
      </div>
      <div class="col-md-6 px-4">
        <p class="subtitle" data-aos="fade-up">{{ .subtitle }}</p>
        <h2 class="section-title" data-aos="fade-up" data-aos-delay="200">{{ .title }}</h2>
        <p class="mb-4" data-aos="fade-up" data-aos-delay="400">{{ .content | markdownify }}</p>
      </div>
    </div>
  </div>
</section>
{{ end }}
{{ end }}
{{"<!-- promo image section -->"| safeHTML}}

{{"<!-- testimonial -->"| safeHTML}}
{{ with .Params.testimonial }}
{{ if .enable }}
<section class="section bg-shape-triangles">
  <div class="container">
    <div class="row">
      <div class="col-lg-12 text-center">
        <p class="subtitle" data-aos="fade-up">{{ .subtitle }}</p>
        <h2 class="section-title" data-aos="fade-up" data-aos-delay="100">{{ .title }}</h2>
      </div>
    </div>
    <div class="row testimonial-slider" data-aos="fade-up" data-aos-delay="200">
      {{ range .testimonial_item }}
      <div class="col-lg-4">
        <div class="card px-4 py-5 border-0 rounded-lg shadow text-center card-border-bottom mb-5 mt-3">
          <i class="fa fa-quote-right icon-quote mb-4 mx-auto"></i>
          <p class="mb-4">{{ .content | markdownify }}</p>
          <h4>{{ .name }}</h4>
          <span class="h6">{{ .designation }}</span>
        </div>
      </div>
      {{ end }}
    </div>
  </div>
</section>
{{ end }}
{{ end }}
{{"<!-- testimonial -->"| safeHTML}}

{{"<!-- download part start -->"| safeHTML}}
{{ with .Params.download }}
{{ if .enable }}
<section class="bg-triangles bg-gradient-primary">
  <div class="container">
    <div class="row">
      <div class="col-lg-6 text-center text-lg-left">
        <div class="section">
          <h2 class="section-title text-white" data-aos="fade-up">{{ .title }}</h2>
          <p class="text-white mb-4" data-aos="fade-up" data-aos-delay="200">{{ .content | markdownify }}</p>
          <ul class="list-inline">
            <li class="list-inline-item mb-3 mb-lg-0">
              {{ with .playstore }}
              <a class="btn btn-white" href="{{ .link | safeURL }}" data-aos="zoom-in" data-aos-delay="400"><img src="{{`images/icon/google-play.png` | absURL }}" class="img-fluid mr-2" alt="">{{ .label }}</a>
              {{ end }}
            </li>
            <li class="list-inline-item">
              {{ with .appstore }}
              <a class="btn btn-outline-white" href="{{ .link | safeURL }}" data-aos="zoom-in" data-aos-delay="500"><i class="fab fa-apple fa-2x mr-2" style="vertical-align:-8px"></i>{{ .label }}</a>
              {{ end }}
            </li>
          </ul>
        </div>
      </div>
      <div class="col-lg-6 align-self-end">
        <img src="{{ .image | absURL }}" class="img-fluid w-100" alt="mobile" data-aos="fade-left" data-aos-delay="600">
      </div>
    </div>
  </div>
</section>
{{ end }}
{{ end }}
{{"<!-- download -->"| safeHTML}}

{{"<!-- pricing -->"| safeHTML}}
{{ with .Params.pricing }}
{{ if .enable }}
<section class="section section-lg-bottom">
  <div class="container">
    <div class="row">
      <div class="col-lg-12 text-center">
        {{ with site.GetPage (string .section) }}
        <p class="subtitle" data-aos="fade-up">{{ .Params.pricing.subtitle }}</p>
        <h2 class="section-title" data-aos="fade-up" data-aos-delay="100">{{ .Params.pricing.title }}</h2>
        {{ end }}
      </div>
      
      {{$show_items:= .show_items}}
      {{ with site.GetPage (string .section) }}

      {{ range $index, $element:= first ($show_items | default 3) .Params.pricing.pricing_table }}
      {{ $delay := mul $index 200}}
      <div class="col-lg-4 col-sm-6 mb-lg-0 mb-4" data-aos="fade-up" data-aos-delay="{{$delay}}">
        <div class="pricing-table position-relative text-center px-4 py-5 rounded-lg shadow transition bg-white">
          <span class="badge badge-pill badge-light font-weight-medium mb-3 py-2 px-4 text-primary">{{ .title }}</span>
          <div class="h1 text-dark">{{ .price }}<span class="paragraph text-lowercase"> / {{ .unit }}</span></div>
          <h5 class="mb-4 font-weight-normal text-color">{{ .description }}</h5>
          <hr>
          <ul class="list-unstyled my-4">
            {{ range .services }}
            <li class="my-3">{{ . }}</li>
            {{ end }}
          </ul>
          <a href="{{ .link | safeURL }}" class="btn btn-outline-primary">Select Plan</a>
        </div>
      </div>
      {{ end }}
      {{ end }}
    </div>
  </div>
</section>
{{ end }}
{{"<!-- pricing -->"| safeHTML}}
{{ end }}
{{ end }}