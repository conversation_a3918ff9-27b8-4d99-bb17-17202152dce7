<div class="col-lg-4">
  <div class="rounded-sm shadow bg-white pb-4">
    <div class="widget">
      <h4>{{ i18n "search" }}</h4>
      <form action="{{ `search`| relLangURL }}">
        <div class="position-relative">
          <input id="search-query" name="s" type="search" placeholder="Search here" class="border-bottom form-control rounded-0 px-0">
          <button class="search-btn" type="submit"><i class="fa fa-search"></i></button>
        </div>
      </form>
    </div>
    <div class="widget">
      <h4>{{ i18n "category" }}</h4>
      {{- if isset site.Taxonomies "categories" }}
      {{- if not (eq (len site.Taxonomies.categories) 0) }}
      <ul class="list-styled list-bordered">
        {{- range $name, $items := site.Taxonomies.categories }}
        <li><a class="text-color d-inline-block py-3" href="{{ `categories/` | relLangURL }}{{ $name | urlize | lower }}">{{ $name | title | humanize }}</a></li>
        {{- end }}
      </ul>
      {{- end }}
      {{- end }}
    </div>
    <div class="widget">
      <h4>{{ i18n "latest_article" }}</h4>
      <ul class="list-unstyled list-bordered">
        {{ range first 3 (where site.RegularPages "Section" "in" site.Params.mainSections ) }}
        <li class="media border-bottom py-3">
          <img src="{{ .Params.Image | absURL }}" class="rounded-sm mr-3" alt="post-thumb">
          <div class="media-body">
            <h6 class="mt-0"><a href="{{ .Permalink }}" class="text-dark">{{ .Title }}</a></h6>
            <p class="mb-0 text-color">{{ .PublishDate.Format "Jan 02, 2006" }}</p>
          </div>
        </li>
        {{ end }}
      </ul>
    </div>
    <div class="widget">
      <h4>{{ i18n "tags" }}</h4>
      {{- if isset site.Taxonomies "tags" }}
      {{- if not (eq (len site.Taxonomies.tags) 0) }}
      <ul class="list-inline tag-list mt-4">
        {{- range $name, $items := site.Taxonomies.tags }}
        <li class="list-inline-item mb-3"><a href="{{ `tags/` | relLangURL }}{{ $name | urlize | lower }}" class="text-color shadow">{{ $name | humanize }}</a></li>
        {{- end }}
      </ul>
      {{- end }}
      {{- end }}
    </div>
  </div>
</div>