{{ if .Params.gallery.enable }}
{{ with .Params.gallery }}
<section class="section bg-white">
	<div class="container">
		<div class="row">
			<div class="col-lg-12 text-center">
				<h2 class="section-title" data-aos="fade-up">{{ .title }}</h2>
				<p class="mb-5" data-aos="fade-up" data-aos-delay="200">{{ .description }}</p>
			</div>
		</div>
		<div class="row">
			{{ range $index, $image := .gallery_item }}
			{{ $delay := mul $index 100 }}
			<div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="{{ $delay }}">
				<a href="{{ $image.image | absURL }}" class="venobox" data-gall="gallery">
					<img src="{{ $image.image | absURL }}" alt="{{ $image.caption }}" class="img-fluid rounded shadow w-100" style="height: 250px; object-fit: cover;">
					{{ if $image.caption }}
					<div class="mt-2 text-center">
						<p class="mb-0 text-muted">{{ $image.caption }}</p>
					</div>
					{{ end }}
				</a>
			</div>
			{{ end }}
		</div>
	</div>
</section>
{{ end }}
{{ end }}