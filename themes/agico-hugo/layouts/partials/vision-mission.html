{{ with .Params.vision_mission }}
{{ if .enable }}
<section class="section vision-mission bg-light position-relative overflow-hidden">
  <div class="container">
    <div class="row g-4">
      {{ range .items }}
      <div class="col-lg-4" data-aos="fade-up">
        <div class="vision-card h-100">
          <div class="card border-0 bg-white h-100">
            <div class="card-body p-5">
              <div class="vision-icon mb-4">
                <svg class="circle-bg" viewBox="0 0 100 100">
                  <circle cx="50" cy="50" r="48" />
                </svg>
                <span class="icon">
                  {{ if in (lower .title) "vision" }}
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24">
                    <path fill="none" d="M0 0h24v24H0z"/>
                    <path d="M12 3c5.392 0 9.878 3.88 10.819 9-.94 5.12-5.427 9-10.819 9-5.392 0-9.878-3.88-10.819-9C2.121 6.88 6.608 3 12 3zm0 16a9.005 9.005 0 0 0 8.777-7 9.005 9.005 0 0 0-17.554 0A9.005 9.005 0 0 0 12 19zm0-2.5a4.5 4.5 0 1 1 0-9 4.5 4.5 0 0 1 0 9zm0-2a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5z"/>
                  </svg>
                  {{ else if in (lower .title) "mission" }}
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24">
                    <path fill="none" d="M0 0h24v24H0z"/>
                    <path d="M12 22l-4-4h8l-4 4zm5.364-10.364L12 16l-5.364-4.364a7.5 7.5 0 1 1 10.728 0zM12 13.5L14.5 11a3.5 3.5 0 1 0-5 0L12 13.5z"/>
                  </svg>
                  {{ else }}
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24">
                    <path fill="none" d="M0 0h24v24H0z"/>
                    <path d="M3.783 2.826L12 1l8.217 1.826a1 1 0 0 1 .783.976v9.987a6 6 0 0 1-2.672 4.992L12 23l-6.328-4.219A6 6 0 0 1 3 13.79V3.802a1 1 0 0 1 .783-.976z"/>
                  </svg>
                  {{ end }}
                </span>
              </div>
              <h3 class="h3 mb-4 vision-title">{{ .title }}</h3>
              <div class="vision-content">{{ .content | markdownify }}</div>
            </div>
          </div>
        </div>
      </div>
      {{ end }}
    </div>
  </div>
  
  <!-- Background decoration -->
  <div class="decoration-shape"></div>
</section>

<style>
.vision-mission {
  padding: 100px 0;
}

.vision-card {
  position: relative;
  transition: all 0.3s ease;
}

.vision-card:hover {
  transform: translateY(-10px);
}

.vision-card .card {
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.vision-card:hover .card {
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.12);
}

.vision-icon {
  position: relative;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.vision-icon .circle-bg {
  position: absolute;
  width: 100%;
  height: 100%;
  fill: none;
  stroke: {{ site.Params.variables.primary_color | default "#2962ff" }};
  stroke-width: 2;
  stroke-dasharray: 300;
  stroke-dashoffset: 300;
  transition: stroke-dashoffset 0.6s ease;
}

.vision-card:hover .circle-bg {
  stroke-dashoffset: 0;
}

.vision-icon .icon {
  position: relative;
  z-index: 1;
}

.vision-icon .icon svg {
  width: 32px;
  height: 32px;
  fill: {{ site.Params.variables.primary_color | default "#2962ff" }};
}

.vision-title {
  color: #232323;
  font-weight: 600;
  position: relative;
  padding-bottom: 15px;
}

.vision-title:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 50px;
  height: 2px;
  background: {{ site.Params.variables.primary_color | default "#2962ff" }};
  transition: width 0.3s ease;
}

.vision-card:hover .vision-title:after {
  width: 100px;
}

.vision-content {
  color: #6c757d;
  line-height: 1.8;
}

.decoration-shape {
  position: absolute;
  top: 0;
  right: 0;
  width: 200px;
  height: 200px;
  background: linear-gradient(45deg, 
    {{ site.Params.variables.primary_color | default "#2962ff" }}22,
    {{ site.Params.variables.secondary_color | default "#0d47a1" }}11
  );
  border-radius: 0 0 0 100%;
  z-index: 0;
}

@media (max-width: 991px) {
  .vision-mission {
    padding: 60px 0;
  }
  
  .vision-card {
    margin-bottom: 30px;
  }
}
</style>
{{ end }}
{{ end }}