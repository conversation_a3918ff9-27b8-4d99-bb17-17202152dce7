{{"<!-- navigation -->" | safeHTML }}
<div class="naviagtion fixed-top transition">
  <div class="container">
    <nav class="navbar navbar-expand-lg navbar-dark p-0">
      <a class="navbar-brand p-0" href="{{ site.BaseURL | relLangURL }}"><img src="{{ site.Params.logo | absURL }}" alt="{{ site.Title }}"></a>
      <button class="navbar-toggler border-0" type="button" data-toggle="collapse" data-target="#navigation" aria-controls="navigation"
        aria-expanded="false" aria-label="Toggle navigation">
        <i class="fa fa-bars text-white h3 mb-0"></i>
      </button>

      <div class="collapse navbar-collapse text-center" id="navigation">
        <ul class="navbar-nav mx-auto">
          <li class="nav-item">
            <a class="nav-link text-white text-capitalize" href="{{ site.BaseURL | relLangURL }}">{{ site.Params.Home }}</a>
          </li>
          {{ range site.Menus.main }}
          {{ if .HasChildren }}
          <li class="nav-item dropdown">
            <a class="nav-link text-white text-capitalize dropdown-toggle" href="#" role="button" data-toggle="dropdown" aria-haspopup="true"
              aria-expanded="false">
              {{ .Name }}
            </a>
            <div class="dropdown-menu" >
              {{ range .Children }}
              <a class="dropdown-item text-color" href="{{ .URL | relLangURL }}">{{ .Name }}</a>
              {{ end }}
            </div>
          </li>
          {{ else }}
          <li class="nav-item">
            <a class="nav-link text-white text-capitalize" href="{{ .URL | relLangURL }}">{{ .Name }}</a>
          </li>
          {{ end }}
          {{ end }}
        </ul>

        <!-- Language List -->
        {{ if .IsTranslated }}
        <select id="select-language" onchange="location = this.value;">
            {{ $siteLanguages := site.Languages}}
          {{ $pageLang := .Page.Lang}}
          {{ range .Page.AllTranslations }}
          {{ $translation := .}}
          {{ range $siteLanguages }}
          {{ if eq $translation.Lang .Lang }}
          {{ $selected := false }}
          {{ if eq $pageLang .Lang}}
          <option id="{{ $translation.Language }}" value="{{ $translation.Permalink }}" selected>{{ .LanguageName }}</option>
          {{ else }}
          <option id="{{ $translation.Language }}" value="{{ $translation.Permalink }}">{{ .LanguageName }}</option>
          {{ end }}
          {{ end }}
          {{ end }}
          {{ end }}
        </select>
        {{ end }}

        {{ if site.Params.navigation_button.enable }}
        {{ "<!-- get start btn -->" | safeHTML }}
        <a href="{{ site.Params.navigation_button.link | absURL }}" class="btn btn-outline-primary text-white ml-lg-3">{{ site.Params.navigation_button.label }}</a>
        {{ end }}
      </div>
    </nav>
  </div>
</div>
{{"<!-- nav part end -->" | safeHTML }}