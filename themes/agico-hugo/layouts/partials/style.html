<!-- DNS preconnect -->
<meta http-equiv="x-dns-prefetch-control" content="on">
<link rel="preconnect" href="//ajax.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin="anonymous">
<link rel="preconnect" href="https://use.fontawesome.com" crossorigin>
<link rel="preconnect" href="//cdnjs.cloudflare.com">
<link rel="preconnect" href="//www.googletagmanager.com">
<link rel="preconnect" href="//www.google-analytics.com">
<link rel="dns-prefetch" href="https://fonts.gstatic.com">
<link rel="dns-prefetch" href="https://use.fontawesome.com">
<link rel="dns-prefetch" href="//ajax.googleapis.com">
<link rel="dns-prefetch" href="//cdnjs.cloudflare.com">
<link rel="dns-prefetch" href="//www.googletagmanager.com">
<link rel="dns-prefetch" href="//www.google-analytics.com">
<link rel="dns-prefetch" href="//fonts.googleapis.com">
<link rel="dns-prefetch" href="//connect.facebook.net">
<link rel="dns-prefetch" href="//platform.linkedin.com">
<link rel="dns-prefetch" href="//platform.twitter.com">

<!-- plugins + stylesheet -->
{{ $styles := slice }}
{{ range site.Params.plugins.css }}
{{ if findRE "^http" .link }}
<link crossorigin="anonymous" media="all" rel="stylesheet" href="{{ .link | relURL }}" {{.attributes | safeHTMLAttr}} >
{{ else }}
{{ $styles = $styles | append (resources.Get .link) }}
{{ end }}
{{ end }}
{{ $styles := $styles | append (resources.Get "scss/style.scss" | resources.ExecuteAsTemplate "style.scss" . | toCSS) }}
{{ $styles := $styles | resources.Concat "/css/style.css" | minify  | fingerprint "sha512"}}
<style crossorigin="anonymous" media="all" type="text/css" integrity="{{ $styles.Data.Integrity }}">{{$styles.Content | safeCSS}}</style>

<style>
  .list-styled li::before {
    display: none;
    font-weight: 900;
    content: "\f054";
    font-family: "{{ with site.Params.variables }} {{.icon_font}} {{ end }}";
  }
</style>