{{"<!-- subscription -->" | safeHTML }}
{{ if site.Params.subscription.enable }}
{{ with site.Params.subscription }}
<section class="subscription">
  <div class="container">
    <div class="row align-items-center">
      <div class="col-lg-6">
        <div class="subscription-content">
          <h3 class="mb-4 text-white">{{ i18n "subscribe_title" }}</h3>
          <p class="mb-4 text-white">{{ i18n "subscribe_content" }}</p>
        </div>
      </div>
      <div class="col-lg-6">
        <div id="mc_embed_signup">
          <form action="{{ .mailchimp_form_action }}" method="post" id="mc-embedded-subscribe-form" name="mc-embedded-subscribe-form" class="validate" target="_blank" novalidate>
            <div id="mc_embed_signup_scroll">
              <div class="mc-field-group">
                <input type="email" name="EMAIL" class="required email" id="mce-EMAIL" placeholder="{{ i18n "subscribe_email" }}">
                <input type="submit" value="{{ i18n "subscribe_button" }}" name="subscribe" id="mc-embedded-subscribe" class="button">
              </div>
              <div id="mce-responses" class="clear">
                <div class="response" id="mce-error-response" style="display:none"></div>
                <div class="response" id="mce-success-response" style="display:none"></div>
              </div>
              <div style="position: absolute; left: -5000px;" aria-hidden="true">
                <input type="text" name="{{ .mailchimp_form_name }}" tabindex="-1">
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</section>
{{ end }}
{{ end }}
{{"<!-- /subscription -->" | safeHTML }}