<meta charset="utf-8">
<title>{{.Title | default site.Title}}</title>

<!-- base url -->
{{ if or (eq site.BaseURL "/") (eq site.BaseURL "http://localhost:1313/") (eq site.BaseURL "http://examplesite.org/") (eq site.BaseURL "https://examplesite.org/") (eq site.BaseURL "http://examplesite.com/") (eq site.BaseURL "https://examplesite.com/")}}{{else}}
<base href="{{ site.BaseURL }}">
{{ end }}

<!-- multilingual SEO optimizations -->
{{ if .IsTranslated }}
{{ range .AllTranslations }}
<link rel="alternate" hreflang="{{.Lang}}" href="{{ .RelPermalink | absLangURL }}">
{{ end }}
<link rel="alternate" hreflang="x-default" href="{{ .RelPermalink | absLangURL }}">
{{ end }}

<!-- mobile responsive meta -->
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5">
<meta name="description" content="{{ .Params.Description | default site.Params.description }}">
{{ with site.Params.author }}<meta name="author" content="{{ . }}">{{ end }}
{{ hugo.Generator }}

<!-- favicon -->
<link rel="shortcut icon" href="{{ site.Params.favicon | relURL }}" type="image/x-icon">
<link rel="icon" href="{{ site.Params.favicon | relURL }}" type="image/x-icon">
<meta name="msapplication-TileColor" content="{{site.Params.variables.primary_color | default `#da532c`}}">
<meta name="theme-color" content="{{site.Params.variables.body_color | default `#ffffff` }}">


{{ "<!-- Open Graph image and Twitter Card metadata -->" | safeHTML }}
{{ $image_path := .Params.image | default site.Params.image -}}
{{ $image_path_local :=  printf "static/%s" $image_path -}}
{{ $image_ext := trim (path.Ext $image_path | lower) "." -}}
{{ if fileExists $image_path_local -}}
  <meta property="og:image" content="{{ $image_path | absURL }}" />
  {{/* If not SVG, read image aspect ratio and define Twitter Card and Open Graph width and height  */ -}}
  {{ if ne $image_ext "svg" -}}
    {{ with (imageConfig $image_path_local) -}}
    {{ if (and (gt .Width 144) (gt .Height 144)) -}}
      <meta name="twitter:image" content="{{ $image_path | absURL }}"/>
      <meta name="twitter:card" content="summary{{ if (and (gt .Width 300) (gt .Height 157) (not (eq .Width .Height))) }}_large_image{{ end }}">
    {{ end -}}
    <meta property="og:image:width" content="{{ .Width }}">
    <meta property="og:image:height" content="{{ .Height }}">
    {{ end -}}
  {{ end -}}
  <meta property="og:image:type" content="image/{{ if eq $image_ext `svg` }}svg+xml{{ else }}{{ replaceRE `^jpg$` `jpeg` $image_ext }}{{ end }}">
{{ end -}}
<meta name="twitter:title" content="{{ with .Title }}{{ . }}{{ else }}{{ site.Title }}{{ end }}"/>
<meta name="twitter:description" content="{{ with .Description }}{{ . }}{{ else }}{{ if .IsPage }}{{ .Summary }}{{ else }}{{ with site.Params.description }}{{ . }}{{ end }}{{ end }}{{ end }}"/>
{{ range site.Params.social -}}
  {{ if eq .title "twitter" -}}
    <meta name="twitter:site" content="{{ .link }}"/>
  {{ end -}}
{{ end -}}

{{ template "_internal/opengraph.html" . }}

{{ "<!-- Main Stylesheet -->" | safeHTML }}
<link href="{{ `css/style.css` | absURL }}" rel="stylesheet">
<!-- Custom CSS for banner overlay -->
<link href="{{ `css/custom.css` | absURL }}" rel="stylesheet">

<!-- google analytics -->
{{ template "_internal/google_analytics.html" . }}
