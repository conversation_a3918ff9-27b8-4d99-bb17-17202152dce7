{{"<!-- service -->" | safeHTML }}
{{ with .Params.service }}
{{ if .enable }}
<section class="section">
  <div class="container">
    <div class="row">
      <div class="col-lg-12 text-center">
        <p class="subtitle" data-aos="fade-up">{{ .subtitle }}</p>
        <h2 class="section-title" data-aos="fade-up" data-aos-delay="200">{{ .title }}</h2>
      </div>
      {{ range $index, $element:= .service_item }}
      {{ $delay := mul $index 200}}
      <div class="col-lg-3 col-sm-6 mb-4" data-aos="fade-up" data-aos-delay="{{$delay}}">
        <div class="card border-0 shadow rounded-xs pt-5">
          <div class="card-body">
            <i class="icon-lg icon-{{ .icon_color }} icon-bg-{{ .icon_color }} icon-bg-circle mb-3">
              <i class="{{ .icon }}"></i>
            </i>
            <h4 class="mt-4 mb-3">{{ .title }}</h4>
            <p>{{ .content | markdownify }}</p>
          </div>
        </div>
      </div>
      {{ end }}
    </div>
  </div>
</section>
{{ end }}
{{ end }}
{{"<!-- service -->" | safeHTML }}