{{ "<!-- page-header -->" | safeHTML }}
<section class="page-title page-title-overlay bg-cover" data-background="{{ .Params.page_header_image | absURL }}">
  <div class="container">
    <div class="row">
      <div class="col-lg-7">
        <h1 class="text-white position-relative">{{ .Title }}<span class="watermark-sm">{{ .Params.Watermark }}</span></h1>
        <p class="page-description">{{ .Params.Description | markdownify }}</p>
      </div>
    </div>
  </div>
</section>

<style>
  
.page-description {
  font-size: 24px;
  line-height: 1.6;
  margin: 25px 0;
  font-weight: 600;
  letter-spacing: 0.2px;
  background: linear-gradient(to right, #FFD700, #FFF8DC);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: none;
  filter: drop-shadow(2px 2px 2px rgba(0,0,0,0.3));
}

@media (max-width: 768px) {
  .page-description {
    font-size: 18px;
  }
}
</style>
{{ "<!-- /page-header -->" | safeHTML }}