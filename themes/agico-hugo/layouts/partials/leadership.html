{{ with .Params.leadership }}
{{ if .enable }}
<section class="section pb-0">
  <div class="container">
    <!-- Board of Directors -->
    {{ with .board }}
    <div class="row">
      <div class="col-12 text-center">
        <h2 class="section-title" data-aos="fade-up">{{ .title }}</h2>
      </div>
    </div>
    <div class="row justify-content-center">
      <!-- Board Chair -->
      {{ range .members }}
      {{ if eq .position "Board Chair" }}
      <div class="col-lg-4 col-md-6 mb-4">
        <div class="card border-0 shadow-sm text-center h-100" data-aos="fade-up">
          <div class="position-relative">
            <div class="card-img-top" style="height: 360px;">
              <img src="{{ .image | absURL }}" alt="{{ .name }}" class="img-fluid w-100 h-100" style="object-fit: cover; object-position: top;">
            </div>
          </div>
          <div class="card-body">
            <h5 class="card-title">{{ .name }}</h5>
            <p class="text-muted">{{ .position }}</p>
            <button class="btn btn-primary btn-sm" data-toggle="modal" data-target="#modal-{{ anchorize .name }}">
              Read More
            </button>
          </div>
        </div>
      </div>
      <!-- Modal for Board Chair -->
      <div class="modal fade" id="modal-{{ anchorize .name }}" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">{{ .name }}</h5>
              <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">&times;</span>
              </button>
            </div>
            <div class="modal-body">
              <div class="row">
                <div class="col-md-5">
                  <img src="{{ .image | absURL }}" alt="{{ .name }}" class="img-fluid rounded" style="width: 100%; height: 400px; object-fit: cover; object-position: top;">
                </div>
                <div class="col-md-7">
                  <h6 class="text-muted mb-3">{{ .position }}</h6>
                  <div class="bio-content">
                    {{ range split .bio "\n" }}
                    {{ if ne . "" }}
                    <p class="mb-3">{{ . | markdownify }}</p>
                    {{ end }}
                    {{ end }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      {{ end }}
      {{ end }}

      <!-- Board Members -->
      {{ range .members }}
      {{ if eq .position "Board Members" }}
      {{ range .members }}
      <div class="col-lg-4 col-md-6 mb-4">
        <div class="card border-0 shadow-sm text-center h-100" data-aos="fade-up">
          <div class="position-relative">
            <div class="card-img-top" style="height: 360px;">
              <img src="{{ .image | absURL }}" alt="{{ .name }}" class="img-fluid w-100 h-100" style="object-fit: cover; object-position: top;">
            </div>
          </div>
          <div class="card-body">
            <h5 class="card-title">{{ .name }}</h5>
            <p class="text-muted">{{ .position }}</p>
            <button class="btn btn-primary btn-sm" data-toggle="modal" data-target="#modal-{{ anchorize .name }}">
              Read More
            </button>
          </div>
        </div>
      </div>
      <!-- Modal for Board Member -->
      <div class="modal fade" id="modal-{{ anchorize .name }}" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">{{ .name }}</h5>
              <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">&times;</span>
              </button>
            </div>
            <div class="modal-body">
              <div class="row">
                <div class="col-md-5">
                  <img src="{{ .image | absURL }}" alt="{{ .name }}" class="img-fluid rounded" style="width: 100%; height: 400px; object-fit: cover; object-position: top;">
                </div>
                <div class="col-md-7">
                  <h6 class="text-muted mb-3">{{ .position }}</h6>
                  <div class="bio-content">
                    {{ range split .bio "\n" }}
                    {{ if ne . "" }}
                    <p class="mb-3">{{ . | markdownify }}</p>
                    {{ end }}
                    {{ end }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      {{ end }}
      {{ end }}
      {{ end }}
    </div>
    {{ end }}

    <!-- Management Team -->
    {{ with .management }}
    <div class="row mt-5">
      <div class="col-12 text-center">
        <h2 class="section-title" data-aos="fade-up">{{ .title }}</h2>
      </div>
    </div>
    <div class="row justify-content-center">
      {{ range .members }}
      <div class="col" style="min-width: 200px;">
        <div class="card border-0 shadow-sm text-center h-100" data-aos="fade-up">
          <div class="position-relative">
            <div class="card-img-top" style="height: 280px;">
              <img src="{{ .image | absURL }}" alt="{{ .name }}" class="img-fluid w-100 h-100" style="object-fit: cover; object-position: top;">
            </div>
          </div>
          <div class="card-body">
            <h5 class="card-title">{{ .name }}</h5>
            <p class="text-muted">{{ .position }}</p>
            <button class="btn btn-primary btn-sm" data-toggle="modal" data-target="#modal-{{ anchorize .name }}">
              Read More
            </button>
          </div>
        </div>
      </div>
      <!-- Modal for Management Team -->
      <div class="modal fade" id="modal-{{ anchorize .name }}" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">{{ .name }}</h5>
              <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">&times;</span>
              </button>
            </div>
            <div class="modal-body">
              <div class="row">
                <div class="col-md-5">
                  <img src="{{ .image | absURL }}" alt="{{ .name }}" class="img-fluid rounded" style="width: 100%; height: 400px; object-fit: cover; object-position: top;">
                </div>
                <div class="col-md-7">
                  <h6 class="text-muted mb-3">{{ .position }}</h6>
                  <div class="bio-content">
                    {{ range split .bio "\n" }}
                    {{ if ne . "" }}
                    <p class="mb-3">{{ . | markdownify }}</p>
                    {{ end }}
                    {{ end }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      {{ end }}
    </div>
    {{ end }}
  </div>
</section>
{{ end }}
{{ end }}