{{ define "main" }}

<section class="section">
  <div class="container">
    <div class="row justify-content-between">
      {{ if site.Params.gmap.enable }}
      {{ with site.Params.gmap }}
      <div class="col-lg-7 rounded-xs mobile-height mb-5 mb-lg-0">
        <div id="map_canvas" style="width: 100%; height: 450px;"></div>
      </div>

      <script>
      // Define initMap in global scope
      window.initMap = function() {
        try {
          // Define coordinates directly to ensure proper number handling
          const coordinates = {
            lat: {{ .map_latitude }},
            lng: {{ .map_longitude }}
          };

          // Validate coordinates
          if (!coordinates.lat || !coordinates.lng || 
              isNaN(coordinates.lat) || isNaN(coordinates.lng) || 
              !isFinite(coordinates.lat) || !isFinite(coordinates.lng)) {
            console.error('Invalid coordinates:', coordinates);
            return;
          }

          // Create map with validated coordinates
          const map = new google.maps.Map(document.getElementById('map_canvas'), {
            center: coordinates,
            zoom: {{ default 17 .map_zoom }},
            mapTypeId: google.maps.MapTypeId.ROADMAP,
            disableDefaultUI: true,
            zoomControl: true
          });

          // Add marker
          new google.maps.Marker({
            position: coordinates,
            map: map
          });

          console.log('Map initialized with coordinates:', coordinates);
        } catch (error) {
          console.error('Error initializing map:', error);
        }
      }

      // Load Google Maps API
      function loadGoogleMaps() {
        const script = document.createElement('script');
        script.src = "https://maps.googleapis.com/maps/api/js?key={{ .gmap_api }}&callback=initMap";
        script.async = true;
        script.defer = true;
        document.head.appendChild(script);
      }

      // Wait for DOM to be ready
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', loadGoogleMaps);
      } else {
        loadGoogleMaps();
      }
      </script>
      {{ end }}
      {{ end }}
      <div class="col-lg-4">
        <div class="p-5 rounded-xs shadow">
          <h3 class="text-dark border-bottom mb-5 pb-4">{{ i18n "keep_in_touch" }}</h3>
          <ul class="list-unstyled">
            {{ with site.Params.mobile }}
            <li class="d-flex mb-4" data-aos="fade-up" data-aos-delay="100">
              <i class="fa fa-phone icon-primary"></i>
              <div class="pl-3">
                <h6 class="text-dark">Mobile</h6>
                <p>{{ . | markdownify }}</p>
              </div>
            </li>
            {{ end }}
            
            {{ with site.Params.email }}
            <li class="d-flex mb-4" data-aos="fade-up" data-aos-delay="100">
              <i class="fas fa-envelope icon-primary"></i>
              <div class="pl-3">
                <h6 class="text-dark">Email</h6>
                <p>{{ . | markdownify }}</p>
              </div>
            </li>
            {{ end }}
            
            {{ with site.Params.address }}
            <li class="d-flex mb-4" data-aos="fade-up" data-aos-delay="100">
              <i class="fas fa-map icon-primary"></i>
              <div class="pl-3">
                <h6 class="text-dark">Address</h6>
                <p>{{ . | markdownify }}</p>
              </div>
            </li>
            {{end }}
          </ul>
        </div>
      </div>
    </div>
  </div>
</section>

{{/*  <section class="section section-lg-bottom bg-light">
  <div class="container">
    <div class="row">
      <div class="col-lg-12 text-center">
        <p class="subtitle">{{ i18n "form_subtitle" }}</p>
        <h2 class="section-title">{{ i18n "form_title" }}</h2>
      </div>
      <div class="col-lg-12 text-center p-0">
        <form action="{{ site.Params.contact_form_action }}" method="post" class="row">
          <div class="col-lg-6">
            <input type="text" class="form-control mb-4" placeholder="Your email">
          </div>
          <div class="col-lg-6">
            <input type="text" class="form-control mb-4" placeholder="Your Name">
          </div>
          <div class="col-lg-12">
            <input type="text" class="form-control mb-4" placeholder="Subject">
          </div>
          <div class="col-lg-12">
            <textarea name="message" class="form-control mb-4" placeholder="Message"></textarea>
          </div>
          <div class="col-12">
            <button type="submit" class="btn btn-primary">{{ i18n "submit_now"}}</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</section>  */}}

{{ end }}