{{ define "main" }}

{{"<!-- about agico -->" | safeHTML }}
{{ with .Params.about }}
{{ if .enable }}
<section class="section">
  <div class="container">
    <div class="row">
      <div class="col-md-6 align-self-center pr-lg-4">
        <p class="subtitle" data-aos="fade-up">{{ .subtitle }}</p>
        <h2 class="section-title" data-aos="fade-up" data-aos-delay="200">{{ .title }}</h2>
        <p data-aos="fade-up" data-aos-delay="400">{{ .content | markdownify }}</p>
        {{ if .button.enable }}
        {{ with .button }}
        <a class="btn btn-primary" href="{{ .link | absLangURL }}" data-aos="fade-up" data-aos-delay="600">{{ .label }}</a>
        {{ end }}
        {{ end }}
      </div>
      <div class="col-md-6 pl-lg-4 mb-4 mb-md-0">
        <div class="position-relative h-100" data-aos="fade-left">
          <div class="row h-100">
            <div class="position-relative" style="min-height: 400px;">
              {{ if .video_bg_image }}
              <img src="{{ .video_bg_image | absURL }}" class="img-fluid w-100 h-100" alt="background-image" style="object-fit: cover; border-radius: 1.5rem;">
              {{ end }}
              {{ if .video_thumbnail }}
              <div style="width: 100%; height: 100%; display: flex; align-items: center; justify-content: center;">
                <img src="{{ .video_thumbnail | absURL }}" class="img-fluid shadow-lg" alt="about-image" style="border-radius: 0.5rem; max-width: 75%;">
              </div>
              {{ end }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
{{ end }}
{{ end }}
{{"<!-- about agico -->" | safeHTML }}

{{"<!-- counter -->" | safeHTML }}
{{ with .Params.funfacts }}
{{ if .enable }}
<section class="bg-light py-5">
  <div class="container">
    <div class="row">
      {{ range .funfact_item }}
      <div class="col-md-3 col-sm-6 mb-4 mb-md-0 text-center">
        <p class="h2 font-weight-light text-dark"><span class="counter" data-count="{{ .count }}">0</span>+</p>
        <h6 class="text-dark font-weight-normal">{{ .name }}</h6>
      </div>
      {{ end }}
    </div>
  </div>
</section>
{{ end }}
{{ end }}
{{"<!-- counter -->" | safeHTML }}

{{"<!-- vision & mission -->" | safeHTML }}
{{ partial "vision-mission.html" . }}

{{"<!-- leadership -->" | safeHTML }}
{{ partial "leadership.html" . }}

{{ if .Params.service.enable }}
{{ with site.GetPage (string .Params.service.section)}}
{{ partial "services.html" . }}
<div class="section pt-0"></div>
{{ end }}
{{else}}
{{/*  <div class="section-lg-bottom"></div>  */}}
{{ end }}

{{ end }}