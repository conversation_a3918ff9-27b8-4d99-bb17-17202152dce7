{{ define "main" }}

<!-- checking blog -->
{{ if or (eq .Section "post") (eq .Section "posts") (eq .Section "blog") (eq .Section "blogs") (eq .Section "news") (eq .Section "categories") (eq .Section "tags") }}
<section class="section section-lg-bottom">
  <div class="container">
    <div class="row">
      <div class="col-lg-8">
        <div class="row">
          {{ $paginator := .Paginate .Data.Pages }}
          {{ range $paginator.Pages }}
          {{ "<!-- blog post -->" | safeHTML }}
          <div class="col-sm-6 mb-4">
            <article class="card border-0 shadow rounded-lg">
              <img src="{{ .Params.Image | absURL }}" class="img-fluid card-img-top rounded-top-lg" alt="post-thumb">
              <div class="card-body">
                <p class="card-date">{{ .PublishDate.Format "Jan 02, 2006" }}</p>
                <h5><a class="text-dark" href="{{ .Permalink }}">{{ .Title }}</a></h5>
              </div>
            </article>
          </div>
          {{ end }}
          {{"<!-- pagination -->" | safeHTML }}
          <div class="col-12 mt-5">
            {{ $paginator := .Paginator }}
            <!-- Number of links either side of the current page. -->
            {{ $adjacent_links := 2 }}
            <!-- $max_links = ($adjacent_links * 2) + 1 -->
            {{ $max_links := (add (mul $adjacent_links 2) 1) }}
            <!-- $lower_limit = $adjacent_links + 1 -->
            {{ $lower_limit := (add $adjacent_links 1) }}
            <!-- $upper_limit = $paginator.TotalPages - $adjacent_links -->
            {{ $upper_limit := (sub $paginator.TotalPages $adjacent_links) }}
            <!-- If there's more than one page. -->
            {{ if gt $paginator.TotalPages 1 }}
            <nav aria-label="Page navigation">
              <ul class="pagination justify-content-center">
                <!-- First page. -->
                {{ if ne $paginator.PageNumber 1 }}
                <li class="page-item">
                  <a class="page-link hover-ripple" href="{{ $paginator.First.URL }}">
                    ««
                  </a>
                </li>
                {{ end }}
                <!-- Previous page. -->
                {{ if $paginator.HasPrev }}
                <li class="page-item">
                  <a href="{{ $paginator.Prev.URL }}" class="page-link hover-ripple">
                    «
                  </a>
                </li>
                {{ end }}
                <!-- Page numbers. -->
                {{ range $paginator.Pagers }}
                  {{ $.Scratch.Set "page_number_flag" false }}
                  <!-- Advanced page numbers. -->
                  {{ if gt $paginator.TotalPages $max_links }}
                    <!-- Lower limit pages. -->
                    <!-- If the user is on a page which is in the lower limit.  -->
                    {{ if le $paginator.PageNumber $lower_limit }}
                      <!-- If the current loop page is less than max_links. -->
                      {{ if le .PageNumber $max_links }}
                        {{ $.Scratch.Set "page_number_flag" true }}
                      {{ end }}
                    <!-- Upper limit pages. -->
                    <!-- If the user is on a page which is in the upper limit. -->
                    {{ else if ge $paginator.PageNumber $upper_limit }}
                      <!-- If the current loop page is greater than total pages minus $max_links -->
                      {{ if gt .PageNumber (sub $paginator.TotalPages $max_links) }}
                        {{ $.Scratch.Set "page_number_flag" true }}
                      {{ end }}
                    <!-- Middle pages. -->
                    {{ else }}
                      {{ if and ( ge .PageNumber (sub $paginator.PageNumber $adjacent_links) ) ( le .PageNumber (add $paginator.PageNumber $adjacent_links) ) }}
                        {{ $.Scratch.Set "page_number_flag" true }}
                      {{ end }}
                    {{ end }}
                  <!-- Simple page numbers. -->
                  {{ else }}
                    {{ $.Scratch.Set "page_number_flag" true }}
                  {{ end }}
                  <!-- Output page numbers. -->
                  {{ if eq ($.Scratch.Get "page_number_flag") true }}
                    <li class="page-item{{ if eq . $paginator }} page-item active {{ end }}">
                      <a href="{{ .URL }}" class="page-link hover-ripple">
                        {{ .PageNumber }}
                      </a>
                    </li>
                  {{ end }}
                {{ end }}
                <!-- Next page. -->
                {{ if $paginator.HasNext }}
                <li class="page-item">
                  <a href="{{ $paginator.Next.URL }}" class="page-link hover-ripple">
                    »
                  </a>
                </li>
                {{ end }}
                <!-- Last page. -->
                {{ if ne $paginator.PageNumber $paginator.TotalPages }}
                <li class="page-item">
                  <a class="page-link hover-ripple" href="{{ $paginator.Last.URL }}">
                    »»
                  </a>
                </li>
                {{ end }}
              </ul>
            </nav>
            {{ end }}
          </div>
          {{"<!-- /pagination -->" | safeHTML }}
        </div>
      </div>
      {{ partial "sidebar.html" . }}
    </div>
  </div>
</section>
{{ else }}

<section class="section">
  <div class="container">
    <div class="row">
      <div class="col-12">
        <div class="content">
          {{.Content }}
        </div>
      </div>
    </div>
  </div>
</section>

{{ end }}

{{ end }}