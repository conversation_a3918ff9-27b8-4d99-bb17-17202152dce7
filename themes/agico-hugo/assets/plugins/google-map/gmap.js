function map() {

	window.marker = null;

	function initialize() {
		var map;
		var latitude = $('#map_canvas').attr('data-latitude');
		var longitude = $('#map_canvas').attr('data-longitude');
		var mapMarker = $('#map_canvas').attr('data-marker');
		var mapMarkerLabel = $('#map_canvas').attr('data-marker-label');
		var mapZoom = parseInt($('#map_canvas').attr('data-zoom'));
		var nottingham = new google.maps.LatLng(latitude, longitude);
		var style = [{
				"featureType": "water",
				"elementType": "geometry",
				"stylers": [{
						"color": "#e9e9e9"
					},
					{
						"lightness": 17
					}
				]
			},
			{
				"featureType": "landscape",
				"elementType": "geometry",
				"stylers": [{
						"color": "#f5f5f5"
					},
					{
						"lightness": 20
					}
				]
			},
			{
				"featureType": "road.highway",
				"elementType": "geometry.fill",
				"stylers": [{
						"color": "#ffffff"
					},
					{
						"lightness": 17
					}
				]
			},
			{
				"featureType": "road.highway",
				"elementType": "geometry.stroke",
				"stylers": [{
						"color": "#ffffff"
					},
					{
						"lightness": 29
					},
					{
						"weight": 0.2
					}
				]
			},
			{
				"featureType": "road.arterial",
				"elementType": "geometry",
				"stylers": [{
						"color": "#ffffff"
					},
					{
						"lightness": 18
					}
				]
			},
			{
				"featureType": "road.local",
				"elementType": "geometry",
				"stylers": [{
						"color": "#ffffff"
					},
					{
						"lightness": 16
					}
				]
			},
			{
				"featureType": "poi",
				"elementType": "geometry",
				"stylers": [{
						"color": "#f5f5f5"
					},
					{
						"lightness": 21
					}
				]
			},
			{
				"featureType": "poi.park",
				"elementType": "geometry",
				"stylers": [{
						"color": "#dedede"
					},
					{
						"lightness": 21
					}
				]
			},
			{
				"elementType": "labels.text.stroke",
				"stylers": [{
						"visibility": "on"
					},
					{
						"color": "#ffffff"
					},
					{
						"lightness": 16
					}
				]
			},
			{
				"elementType": "labels.text.fill",
				"stylers": [{
						"saturation": 36
					},
					{
						"color": "#333333"
					},
					{
						"lightness": 40
					}
				]
			},
			{
				"elementType": "labels.icon",
				"stylers": [{
					"visibility": "off"
				}]
			},
			{
				"featureType": "transit",
				"elementType": "geometry",
				"stylers": [{
						"color": "#f2f2f2"
					},
					{
						"lightness": 19
					}
				]
			},
			{
				"featureType": "administrative",
				"elementType": "geometry.fill",
				"stylers": [{
						"color": "#fefefe"
					},
					{
						"lightness": 20
					}
				]
			},
			{
				"featureType": "administrative",
				"elementType": "geometry.stroke",
				"stylers": [{
						"color": "#fefefe"
					},
					{
						"lightness": 17
					},
					{
						"weight": 1.2
					}
				]
			}
		];
		var mapOptions = {
			center: nottingham,
			mapTypeId: google.maps.MapTypeId.ROADMAP,
			backgroundColor: "#000",
			zoom: mapZoom,
			panControl: false,
			zoomControl: true,
			mapTypeControl: false,
			scaleControl: false,
			streetViewControl: false,
			overviewMapControl: false,
			zoomControlOptions: {
				style: google.maps.ZoomControlStyle.LARGE
			}
		}
		map = new google.maps.Map(document.getElementById('map_canvas'), mapOptions);
		var mapType = new google.maps.StyledMapType(style, {
			name: "Grayscale"
		});
		map.mapTypes.set('grey', mapType);
		map.setMapTypeId('grey');
		var marker_image = mapMarker;
		var pinIcon = new google.maps.MarkerImage(marker_image, null, null, null, new google.maps.Size(46, 58));
		marker = new google.maps.Marker({
			position: nottingham,
			map: map,
			icon: pinIcon,
			title: mapMarkerLabel
		});
	}
	var map = document.getElementById('map_canvas');
	if (map != null) {
		google.maps.event.addDomListener(window, 'load', initialize);
	}
}