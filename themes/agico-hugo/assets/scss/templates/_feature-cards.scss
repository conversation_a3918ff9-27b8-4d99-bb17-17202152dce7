@import "bootstrap/scss/functions";
@import "bootstrap/scss/variables";
@import "bootstrap/scss/mixins";

.feature-card {
  height: 100%;
  
  &-image {
    min-height: 200px;
    background: fade-out($primary-color, 0.95);
    border-radius: $border-radius $border-radius 0 0;
    padding: 1.5rem;
    
    img {
      transition: transform 0.3s ease;
    }
    
    &:hover img {
      transform: scale(1.05);
    }
  }

  &-content {
    padding: 1.5rem;
  }
}

@include media-breakpoint-up(md) {
  .feature-card-image {
    min-height: 250px;
  }
}
