/* sidebar */
.widget {
  padding: 40px 30px 0;
}

.search-btn {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  background: transparent;
  border: 0;
}

.tag-list {
  li {
    a {
      padding: 12px 15px;
      display: block;
      border-radius: 40px;

      &:hover {
        background: $btn-gradient-primary;
        color: $white !important;
      }
    }
  }
}

/* sidebar */

.pagination .page-item .page-link {
  background: transparent;
  border: none;
  color: $primary-color;
  font-family: "Open Sans", sans-serif;
  font-weight: 600;
  transition: .3s ease;
  height: 50px;
  text-align: center;
  border-radius: 10px;
  margin: 10px;
  width: 50px;
  line-height: 33px;
  box-shadow: 0px 5px 15px 0px rgba(51, 77, 128, 0.12)
}

.pagination .page-item.active .page-link {
  background: $btn-gradient-primary;
  color: $white;
  box-shadow: 0px 15px 15px 0px rgba(8, 18, 109, 0.1)
}

.pagination .page-item .page-link:hover{
  box-shadow: 0px 15px 15px 0px rgba(8, 18, 109, 0.1)
}

/* blog single */

blockquote {
  box-shadow: inset 0px 7px 0px 0px rgba(255, 255, 255, 0.596);
  padding: 34px 40px 37px 40px;
  background-image: $bg-gradient-secondary;
  border-radius: 10px;
  margin: 40px 0;

  p {
    position: relative;
    font-style: italic;
    color: $white;
  }

  h6 {
    color: $white;
  }
}

.media {
  img {
    height: 75px;
    width: 75px;
    object-fit: cover;
  }

  .reply-btn {
    font-size: 16px;
    font-weight: 500;
  }
}

form {
  textarea {
    height: 180px;
  }
}

/* /blog single */