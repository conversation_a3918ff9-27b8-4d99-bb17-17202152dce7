---
new_page_extension: md
auto_deploy: false
admin_path: ''
webhook_url: 
sections:
- type: heading
  label: Homepage
- type: document
  path: exampleSite/content/english/_index.md
  label: Homepage (en)
- type: document
  path: exampleSite/content/deutsch/_index.md
  label: Homepage (de)
- type: heading
  label: Blog
- type: document
  path: exampleSite/content/english/blog/_index.md
  label: Blog Page (en)
- type: document
  path: exampleSite/content/deutsch/blog/_index.md
  label: Blog Page (de)
- type: directory
  path: exampleSite/content/english/blog
  label: Blog Post (en)
  create: documents
  match: "**/*"
  exclude: _index.md
  templates:
  - post
- type: directory
  path: exampleSite/content/deutsch/blog
  label: Blog Post (de)
  create: documents
  match: "**/*"
  exclude: _index.md
  templates:
  - post
- type: heading
  label: Others Page
- type: document
  path: exampleSite/content/english/contact/_index.md
  label: Contact (en)
- type: document
  path: exampleSite/content/deutsch/contact/_index.md
  label: Contact (de)
- type: document
  path: exampleSite/content/english/pricing/_index.md
  label: Pricing (en)
- type: document
  path: exampleSite/content/deutsch/pricing/_index.md
  label: Pricing (de)
- type: document
  path: exampleSite/content/english/faq/_index.md
  label: FAQ (en)
- type: document
  path: exampleSite/content/deutsch/faq/_index.md
  label: FAQ (de)
- type: heading
  label: All Pages
- type: directory
  path: exampleSite/content
  label: All Pages
  create: all
  match: "**/*"
  exclude: _index.md
  templates:
  - new-page
- type: heading
  label: Configuration
- type: document
  path: exampleSite/config/_default/config.toml
  label: Configuration
- type: document
  path: exampleSite/config/_default/params.toml
  label: Parameters
- type: document
  path: config.toml
  label: Variables & Plugins
  match: "**/*"
- type: document
  path: exampleSite/config/_default/languages.toml
  label: Languages
- type: document
  path: exampleSite/config/_default/menus.en.toml
  label: Menu (en)
- type: document
  path: exampleSite/config/_default/menus.de.toml
  label: Menu (de)
  match: "**/*"
upload_dir: exampleSite/static/images
public_path: "/images"
front_matter_path: ''
use_front_matter_path: false
file_template: ":filename:"
build:
  preview_env:
  - HUGO_ENV=staging
  - HUGO_VERSION=0.81.0
  preview_output_directory: public
  preview_docker_image: forestryio/hugo:latest
  mount_path: "/srv"
  working_dir: "/srv"
  instant_preview_command: hugo server -D -E -F --port 8080 --bind 0.0.0.0 --renderToDisk
    -d public
version: 0.81.0