# Use extended Hugo image for SASS/SCSS support
FROM hugomods/hugo:0.125.2 AS builder

# Set working directory
WORKDIR /src

# Copy package files first (if using npm/yarn)
COPY package*.json ./
COPY yarn*.lock ./

# Install dependencies (if using npm/yarn)
RUN if [ -f package.json ]; then \
    npm install; \
    fi

# Copy the rest of the source code
COPY . .

# Build site with proper environment and base URL
ARG BASE_URL
ENV BASE_URL=${BASE_URL:-https://www.ptblgh.com}

RUN hugo --minify --gc --environment production --baseURL "${BASE_URL}"

# Production stage
FROM nginx:alpine

# Copy custom nginx config
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Copy built site
COPY --from=builder /src/public /usr/share/nginx/html

# Create required directories and set permissions
RUN mkdir -p /var/cache/nginx \
    /var/run \
    /var/log/nginx \
    /var/lib/nginx && \
    chown -R nginx:nginx /var/cache/nginx \
    /var/run \
    /var/log/nginx \
    /var/lib/nginx \
    /usr/share/nginx/html && \
    chmod -R 755 /var/cache/nginx \
    /var/run \
    /var/log/nginx \
    /var/lib/nginx \
    /usr/share/nginx/html

# Add healthcheck
HEALTHCHECK --interval=30s --timeout=3s \
    CMD wget --quiet --tries=1 --spider http://localhost:80/ || exit 1

# Expose port
EXPOSE 80

# Run nginx in the foreground
CMD ["nginx", "-g", "daemon off;"]