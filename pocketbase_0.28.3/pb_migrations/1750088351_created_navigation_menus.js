/// <reference path="../pb_data/types.d.ts" />
migrate((app) => {
  const collection = new Collection({
    "createRule": "@request.auth.id != \"\" && (@request.auth.collectionName = \"_superusers\" || @request.auth.role = \"admin\" || @request.auth.role = \"editor\")",
    "deleteRule": "@request.auth.id != \"\" && (@request.auth.collectionName = \"_superusers\" || @request.auth.role = \"admin\")",
    "fields": [
      {
        "autogeneratePattern": "[a-z0-9]{15}",
        "hidden": false,
        "id": "text3208210256",
        "max": 15,
        "min": 15,
        "name": "id",
        "pattern": "^[a-z0-9]+$",
        "presentable": false,
        "primaryKey": true,
        "required": true,
        "system": true,
        "type": "text"
      },
      {
        "hidden": false,
        "id": "select2523696712",
        "maxSelect": 0,
        "name": "menu_name",
        "presentable": false,
        "required": true,
        "system": false,
        "type": "select",
        "values": [
          "main",
          "footer",
          "sidebar",
          "mobile"
        ]
      },
      {
        "autogeneratePattern": "",
        "hidden": false,
        "id": "text245846248",
        "max": 0,
        "min": 0,
        "name": "label",
        "pattern": "",
        "presentable": false,
        "primaryKey": false,
        "required": true,
        "system": false,
        "type": "text"
      },
      {
        "autogeneratePattern": "",
        "hidden": false,
        "id": "text4101391790",
        "max": 0,
        "min": 0,
        "name": "url",
        "pattern": "",
        "presentable": false,
        "primaryKey": false,
        "required": false,
        "system": false,
        "type": "text"
      },
      {
        "exceptDomains": null,
        "hidden": false,
        "id": "url1415375718",
        "name": "external_url",
        "onlyDomains": null,
        "presentable": false,
        "required": false,
        "system": false,
        "type": "url"
      },
      {
        "hidden": false,
        "id": "number130897217",
        "max": null,
        "min": null,
        "name": "weight",
        "onlyInt": false,
        "presentable": false,
        "required": true,
        "system": false,
        "type": "number"
      },
      {
        "hidden": false,
        "id": "select3571151285",
        "maxSelect": 0,
        "name": "language",
        "presentable": false,
        "required": true,
        "system": false,
        "type": "select",
        "values": [
          "en",
          "fr"
        ]
      },
      {
        "hidden": false,
        "id": "bool1260321794",
        "name": "active",
        "presentable": false,
        "required": true,
        "system": false,
        "type": "bool"
      },
      {
        "hidden": false,
        "id": "select1181691900",
        "maxSelect": 0,
        "name": "target",
        "presentable": false,
        "required": false,
        "system": false,
        "type": "select",
        "values": [
          "_self",
          "_blank",
          "_parent",
          "_top"
        ]
      },
      {
        "autogeneratePattern": "",
        "hidden": false,
        "id": "text2852757930",
        "max": 0,
        "min": 0,
        "name": "css_class",
        "pattern": "",
        "presentable": false,
        "primaryKey": false,
        "required": false,
        "system": false,
        "type": "text"
      },
      {
        "autogeneratePattern": "",
        "hidden": false,
        "id": "text1704208859",
        "max": 0,
        "min": 0,
        "name": "icon",
        "pattern": "",
        "presentable": false,
        "primaryKey": false,
        "required": false,
        "system": false,
        "type": "text"
      },
      {
        "autogeneratePattern": "",
        "hidden": false,
        "id": "text1843675174",
        "max": 0,
        "min": 0,
        "name": "description",
        "pattern": "",
        "presentable": false,
        "primaryKey": false,
        "required": false,
        "system": false,
        "type": "text"
      },
      {
        "hidden": false,
        "id": "json2081018550",
        "maxSize": 0,
        "name": "visibility_rules",
        "presentable": false,
        "required": false,
        "system": false,
        "type": "json"
      },
      {
        "hidden": false,
        "id": "number1796161294",
        "max": null,
        "min": null,
        "name": "menu_depth",
        "onlyInt": false,
        "presentable": false,
        "required": true,
        "system": false,
        "type": "number"
      },
      {
        "hidden": false,
        "id": "autodate94492729_created",
        "name": "created",
        "onCreate": true,
        "onUpdate": false,
        "presentable": false,
        "system": false,
        "type": "autodate"
      },
      {
        "hidden": false,
        "id": "autodate4515039657_updated",
        "name": "updated",
        "onCreate": true,
        "onUpdate": true,
        "presentable": false,
        "system": false,
        "type": "autodate"
      }
    ],
    "id": "pbc_3525715599",
    "indexes": [],
    "listRule": "@request.auth.id != \"\"",
    "name": "navigation_menus",
    "system": false,
    "type": "base",
    "updateRule": "@request.auth.id != \"\" && (@request.auth.collectionName = \"_superusers\" || @request.auth.role = \"admin\" || @request.auth.role = \"editor\")",
    "viewRule": "@request.auth.id != \"\""
  });

  return app.save(collection);
}, (app) => {
  const collection = app.findCollectionByNameOrId("pbc_3525715599");

  return app.delete(collection);
})
