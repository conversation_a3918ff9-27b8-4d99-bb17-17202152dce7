# Task 056: GitHub Actions Workflow

## Task Information
- **Task ID**: 056
- **Title**: GitHub Actions Workflow
- **Category**: CI/CD Pipeline
- **Phase**: 4
- **Priority**: High
- **Estimated Complexity**: Medium
- **Dependencies**: Tasks 026-035 (Content Sync and Hugo Integration)

## Description
Create a comprehensive GitHub Actions workflow that automates the content synchronization, Hugo build process, and deployment pipeline. The workflow should trigger on content changes in PocketBase, sync content, build the Hugo site, deploy the sync service to Railway, and deploy the static site to hosting platform.

## Detailed Requirements
1. Set up PocketBase webhook integration for change detection
2. Create automated content sync workflow triggered by PocketBase changes
3. Implement Hugo build automation
4. Set up deployment to Railway for sync service
5. Configure static site deployment to hosting platform
6. Configure environment-specific workflows
7. Implement error handling and notifications
8. Add workflow monitoring and reporting
9. Create manual trigger options
10. Set up polling fallback for webhook failures

## Technical Specifications
- **Platform**: GitHub Actions
- **Trigger Events**: PocketBase webhooks, repository dispatch, schedule, manual
- **Environments**: Development, Staging, Production
- **Build Tool**: Hugo
- **Sync Service Deployment**: Railway
- **Static Site Deployment**: GitHub Pages, Vercel, or similar
- **Notifications**: Slack, email, or GitHub

## Workflow Architecture
```yaml
# Main workflow structure
name: CMS Content Sync and Deploy

on:
  workflow_dispatch:  # Manual trigger
  schedule:           # Scheduled sync
    - cron: '*/15 * * * *'  # Every 15 minutes
  repository_dispatch: # PocketBase webhook
    types: [content-updated]

jobs:
  sync-content:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
      - name: Setup Node.js
      - name: Install dependencies
      - name: Sync content from PocketBase
      - name: Commit changes
      
  build-hugo:
    needs: sync-content
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
      - name: Setup Hugo
      - name: Build site
      - name: Upload artifacts
      
  deploy:
    needs: build-hugo
    runs-on: ubuntu-latest
    steps:
      - name: Download artifacts
      - name: Deploy to hosting
      - name: Notify completion
```

## PocketBase Change Detection

### 1. Webhook Integration
PocketBase can trigger GitHub Actions through webhooks when content changes occur.

#### PocketBase Webhook Configuration
```javascript
// PocketBase webhook setup (in PocketBase admin or via API)
const webhookConfig = {
  url: "https://api.github.com/repos/your-org/your-repo/dispatches",
  method: "POST",
  headers: {
    "Authorization": "Bearer YOUR_GITHUB_TOKEN",
    "Accept": "application/vnd.github.v3+json",
    "Content-Type": "application/json"
  },
  body: JSON.stringify({
    event_type: "pocketbase-content-changed",
    client_payload: {
      collection: "{{collection}}",
      action: "{{action}}", // create, update, delete
      record_id: "{{record.id}}",
      timestamp: new Date().toISOString()
    }
  }),
  triggers: [
    "collections.pages.create",
    "collections.pages.update",
    "collections.pages.delete",
    "collections.blog_posts.create",
    "collections.blog_posts.update",
    "collections.blog_posts.delete",
    "collections.site_settings.update",
    "collections.navigation_menus.create",
    "collections.navigation_menus.update",
    "collections.navigation_menus.delete"
  ]
};
```

#### GitHub Repository Dispatch Trigger
```yaml
# In .github/workflows/cms-sync-deploy.yml
on:
  repository_dispatch:
    types: [pocketbase-content-changed]
  schedule:
    - cron: '*/15 * * * *'  # Fallback polling every 15 minutes
  workflow_dispatch:  # Manual trigger
```

### 2. Alternative: Polling with Change Detection
If webhooks are not available, implement polling with change detection:

```yaml
# .github/workflows/content-change-detector.yml
name: Content Change Detector

on:
  schedule:
    - cron: '*/5 * * * *'  # Check every 5 minutes

jobs:
  detect-changes:
    runs-on: ubuntu-latest
    outputs:
      changes-detected: ${{ steps.check-changes.outputs.changes }}

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Check for content changes
        id: check-changes
        run: |
          # Get last sync timestamp
          LAST_SYNC=$(cat .last-sync-timestamp 2>/dev/null || echo "1970-01-01T00:00:00Z")

          # Check PocketBase for changes since last sync
          CHANGES=$(curl -s -H "Authorization: Bearer ${{ secrets.POCKETBASE_TOKEN }}" \
            "${{ secrets.POCKETBASE_URL }}/api/collections/pages/records?filter=updated>='$LAST_SYNC'&perPage=1" | \
            jq '.totalItems')

          if [ "$CHANGES" -gt 0 ]; then
            echo "changes=true" >> $GITHUB_OUTPUT
            echo "$(date -u +%Y-%m-%dT%H:%M:%SZ)" > .last-sync-timestamp
            git config --local user.email "<EMAIL>"
            git config --local user.name "GitHub Action"
            git add .last-sync-timestamp
            git commit -m "Update last sync timestamp [skip ci]" || exit 0
            git push
          else
            echo "changes=false" >> $GITHUB_OUTPUT
          fi

  trigger-sync:
    needs: detect-changes
    if: needs.detect-changes.outputs.changes-detected == 'true'
    uses: ./.github/workflows/cms-sync-deploy.yml
```

### 3. PocketBase Webhook Setup Script
```javascript
// scripts/setup-pocketbase-webhooks.js
import PocketBase from 'pocketbase';

const pb = new PocketBase(process.env.POCKETBASE_URL);

// Authenticate as admin
await pb.admins.authWithPassword(
  process.env.POCKETBASE_EMAIL,
  process.env.POCKETBASE_PASSWORD
);

// Create webhook for content changes
const webhook = await pb.send('/api/webhooks', {
  method: 'POST',
  body: {
    name: 'GitHub Actions Content Sync',
    url: `https://api.github.com/repos/${process.env.GITHUB_REPOSITORY}/dispatches`,
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${process.env.GITHUB_TOKEN}`,
      'Accept': 'application/vnd.github.v3+json',
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      event_type: 'pocketbase-content-changed',
      client_payload: {
        collection: '{{collection}}',
        action: '{{action}}',
        record_id: '{{record.id}}',
        timestamp: '{{timestamp}}'
      }
    }),
    events: [
      'collections.pages.*',
      'collections.blog_posts.*',
      'collections.site_settings.*',
      'collections.navigation_menus.*',
      'collections.services.*',
      'collections.team_members.*'
    ],
    active: true
  }
});

console.log('Webhook created:', webhook);
```

## Acceptance Criteria
- [ ] PocketBase webhook integration configured
- [ ] GitHub repository dispatch triggers working
- [ ] Polling fallback mechanism implemented
- [ ] GitHub Actions workflow created
- [ ] Content sync automation working
- [ ] Hugo build process automated
- [ ] Railway deployment for sync service functional
- [ ] Static site deployment pipeline working
- [ ] Environment-specific configurations
- [ ] Error handling and notifications
- [ ] Manual trigger options available
- [ ] Workflow monitoring implemented

## Implementation Steps
1. **Main Workflow File**
   ```yaml
   # .github/workflows/cms-sync-deploy.yml
   name: CMS Content Sync and Deploy

   on:
     repository_dispatch:
       types: [pocketbase-content-changed]

     schedule:
       - cron: '*/15 * * * *'  # Fallback polling every 15 minutes

     workflow_dispatch:
       inputs:
         environment:
           description: 'Deployment environment'
           required: true
           default: 'staging'
           type: choice
           options:
             - staging
             - production
         force_sync:
           description: 'Force full sync regardless of changes'
           type: boolean
           default: false

   env:
     DENO_VERSION: '1.40.0'
     HUGO_VERSION: '0.119.0'
   ```

2. **Content Sync Job**
   ```yaml
   jobs:
     sync-content:
       runs-on: ubuntu-latest
       outputs:
         changes-detected: ${{ steps.sync.outputs.changes }}
         changed-collections: ${{ steps.sync.outputs.collections }}

       steps:
         - name: Checkout repository
           uses: actions/checkout@v4
           with:
             token: ${{ secrets.GITHUB_TOKEN }}
             fetch-depth: 0

         - name: Setup Deno
           uses: denoland/setup-deno@v1
           with:
             deno-version: ${{ env.DENO_VERSION }}

         - name: Parse webhook payload
           id: webhook-data
           run: |
             if [ "${{ github.event_name }}" = "repository_dispatch" ]; then
               echo "Triggered by PocketBase webhook"
               echo "Collection: ${{ github.event.client_payload.collection }}"
               echo "Action: ${{ github.event.client_payload.action }}"
               echo "Record ID: ${{ github.event.client_payload.record_id }}"
               echo "collection=${{ github.event.client_payload.collection }}" >> $GITHUB_OUTPUT
               echo "action=${{ github.event.client_payload.action }}" >> $GITHUB_OUTPUT
               echo "record_id=${{ github.event.client_payload.record_id }}" >> $GITHUB_OUTPUT
               echo "webhook_triggered=true" >> $GITHUB_OUTPUT
             else
               echo "webhook_triggered=false" >> $GITHUB_OUTPUT
             fi

         - name: Sync content from PocketBase
           id: sync
           run: |
             cd sync-framework

             # Set sync parameters based on trigger
             if [ "${{ steps.webhook-data.outputs.webhook_triggered }}" = "true" ]; then
               # Targeted sync for webhook triggers
               deno run --allow-net --allow-read --allow-write --allow-env src/main.ts \
                 --collection="${{ steps.webhook-data.outputs.collection }}" \
                 --incremental=true
             elif [ "${{ github.event.inputs.force_sync }}" = "true" ]; then
               # Force full sync
               deno run --allow-net --allow-read --allow-write --allow-env src/main.ts \
                 --full-sync=true
             else
               # Regular incremental sync
               deno run --allow-net --allow-read --allow-write --allow-env src/main.ts \
                 --incremental=true
             fi
           env:
             POCKETBASE_URL: ${{ secrets.POCKETBASE_URL }}
             POCKETBASE_EMAIL: ${{ secrets.POCKETBASE_EMAIL }}
             POCKETBASE_PASSWORD: ${{ secrets.POCKETBASE_PASSWORD }}

         - name: Commit and push changes
           if: steps.sync.outputs.changes == 'true'
           run: |
             git config --local user.email "<EMAIL>"
             git config --local user.name "GitHub Action"
             git add .

             # Create descriptive commit message
             if [ "${{ steps.webhook-data.outputs.webhook_triggered }}" = "true" ]; then
               COMMIT_MSG="Auto-sync: ${{ steps.webhook-data.outputs.action }} in ${{ steps.webhook-data.outputs.collection }} [skip ci]"
             else
               COMMIT_MSG="Auto-sync content from PocketBase [skip ci]"
             fi

             git commit -m "$COMMIT_MSG" || exit 0
             git push
   ```

3. **Hugo Build Job**
   ```yaml
     build-hugo:
       needs: sync-content
       if: needs.sync-content.outputs.changes-detected == 'true' || github.event_name == 'workflow_dispatch'
       runs-on: ubuntu-latest
       
       steps:
         - name: Checkout repository
           uses: actions/checkout@v4
           with:
             ref: ${{ github.ref }}
         
         - name: Setup Hugo
           uses: peaceiris/actions-hugo@v2
           with:
             hugo-version: ${{ env.HUGO_VERSION }}
             extended: true
         
         - name: Build Hugo site
           run: |
             hugo --minify --environment production
         
         - name: Upload build artifacts
           uses: actions/upload-artifact@v4
           with:
             name: hugo-site
             path: public/
             retention-days: 30
   ```

4. **Railway Sync Service Deployment**
   ```yaml
     deploy-sync-service:
       needs: sync-content
       if: success() && (github.ref == 'refs/heads/main' || github.event_name == 'workflow_dispatch')
       runs-on: ubuntu-latest
       environment: ${{ github.event.inputs.environment || 'production' }}

       steps:
         - name: Checkout repository
           uses: actions/checkout@v4

         - name: Deploy to Railway
           uses: railwayapp/railway-deploy@v1.1.0
           with:
             railway_token: ${{ secrets.RAILWAY_TOKEN }}
             service: sync-service
             dockerfile_path: sync-framework/Dockerfile

   deploy-static-site:
     needs: [sync-content, build-hugo]
     if: success() && (github.ref == 'refs/heads/main' || github.event_name == 'workflow_dispatch')
     runs-on: ubuntu-latest
     environment: ${{ github.event.inputs.environment || 'production' }}

     steps:
       - name: Download build artifacts
         uses: actions/download-artifact@v4
         with:
           name: hugo-site
           path: public/

       - name: Deploy to GitHub Pages
         uses: peaceiris/actions-gh-pages@v3
         if: github.ref == 'refs/heads/main'
         with:
           github_token: ${{ secrets.GITHUB_TOKEN }}
           publish_dir: ./public
           cname: your-domain.com  # Optional: custom domain

       # Alternative: Deploy to Vercel
       - name: Deploy to Vercel
         uses: amondnet/vercel-action@v25
         if: github.event.inputs.deployment_target == 'vercel'
         with:
           vercel-token: ${{ secrets.VERCEL_TOKEN }}
           vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
           vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
           working-directory: ./public
   ```

## Files to Create/Modify
- `.github/workflows/cms-sync-deploy.yml` (main workflow)
- `.github/workflows/content-change-detector.yml` (polling fallback)
- `.github/workflows/manual-deploy.yml` (manual deployment)
- `scripts/setup-pocketbase-webhooks.js` (webhook configuration)
- `scripts/setup-github-secrets.sh` (secrets setup)
- `sync-framework/src/webhook-handler.ts` (webhook payload processing)
- `docs/webhook-setup.md` (webhook documentation)
- `docs/github-actions-setup.md` (workflow documentation)

## Environment Configuration
### Required Secrets
```bash
# PocketBase Configuration
POCKETBASE_URL=https://your-pocketbase-instance.com
POCKETBASE_EMAIL=<EMAIL>
POCKETBASE_PASSWORD=secure_password

# GitHub Token (for webhook triggers)
GITHUB_TOKEN=your_github_personal_access_token  # Needs repo and workflow permissions

# Railway Configuration
RAILWAY_TOKEN=your_railway_token

# Static Site Deployment (choose one)
# GitHub Pages (uses GITHUB_TOKEN automatically)

# Vercel (optional)
VERCEL_TOKEN=your_vercel_token
VERCEL_ORG_ID=your_org_id
VERCEL_PROJECT_ID=your_project_id

# Notification Configuration
SLACK_WEBHOOK_URL=your_slack_webhook
NOTIFICATION_EMAIL=<EMAIL>
```

### Environment Variables
```yaml
env:
  DENO_VERSION: '1.40.0'
  HUGO_VERSION: '0.119.0'
  SYNC_TIMEOUT: '300'
  BUILD_TIMEOUT: '600'
```

## Webhook Setup Instructions

### 1. GitHub Personal Access Token
Create a GitHub Personal Access Token with the following permissions:
- `repo` (Full control of private repositories)
- `workflow` (Update GitHub Action workflows)

### 2. PocketBase Webhook Configuration
```bash
# Run the webhook setup script
cd scripts
deno run --allow-net --allow-env setup-pocketbase-webhooks.js
```

### 3. Test Webhook Integration
```bash
# Test webhook manually
curl -X POST \
  -H "Authorization: Bearer YOUR_GITHUB_TOKEN" \
  -H "Accept: application/vnd.github.v3+json" \
  -H "Content-Type: application/json" \
  -d '{
    "event_type": "pocketbase-content-changed",
    "client_payload": {
      "collection": "pages",
      "action": "update",
      "record_id": "test123",
      "timestamp": "'$(date -u +%Y-%m-%dT%H:%M:%SZ)'"
    }
  }' \
  https://api.github.com/repos/YOUR_ORG/YOUR_REPO/dispatches
```

### 4. Webhook Verification
Add webhook verification to ensure requests come from PocketBase:

```typescript
// sync-framework/src/webhook-handler.ts
export function verifyWebhookSignature(payload: string, signature: string, secret: string): boolean {
  const expectedSignature = crypto
    .createHmac('sha256', secret)
    .update(payload)
    .digest('hex');

  return crypto.timingSafeEqual(
    Buffer.from(signature, 'hex'),
    Buffer.from(expectedSignature, 'hex')
  );
}

export function processWebhookPayload(payload: any) {
  const { collection, action, record_id, timestamp } = payload;

  // Validate payload
  if (!collection || !action || !record_id) {
    throw new Error('Invalid webhook payload');
  }

  // Log webhook event
  console.log(`Webhook received: ${action} on ${collection} (${record_id})`);

  return {
    collection,
    action,
    record_id,
    timestamp: timestamp || new Date().toISOString()
  };
}
```

## Railway Configuration

### railway.json
```json
{
  "$schema": "https://railway.app/railway.schema.json",
  "build": {
    "builder": "DOCKERFILE",
    "dockerfilePath": "sync-framework/Dockerfile"
  },
  "deploy": {
    "startCommand": "deno run --allow-net --allow-read --allow-write --allow-env src/main.ts",
    "healthcheckPath": "/health",
    "healthcheckTimeout": 100,
    "restartPolicyType": "ON_FAILURE",
    "restartPolicyMaxRetries": 10
  }
}
```

### Railway Environment Variables
```bash
# Set in Railway dashboard or via CLI
railway variables set POCKETBASE_URL=https://your-pocketbase.com
railway variables set POCKETBASE_EMAIL=<EMAIL>
railway variables set POCKETBASE_PASSWORD=secure_password
railway variables set SYNC_INTERVAL=300
railway variables set LOG_LEVEL=info
```

## Workflow Variations

### 1. Content-Only Sync
```yaml
# For frequent content updates without full rebuild
name: Content Sync Only
on:
  schedule:
    - cron: '*/5 * * * *'  # Every 5 minutes

jobs:
  sync-only:
    runs-on: ubuntu-latest
    steps:
      - name: Trigger Railway sync service
        run: |
          curl -X POST "${{ secrets.RAILWAY_WEBHOOK_URL }}" \
            -H "Authorization: Bearer ${{ secrets.RAILWAY_TOKEN }}" \
            -d '{"action": "sync"}'
```

### 2. Manual Deployment with Options
```yaml
# For manual deployments with options
name: Manual Deploy
on:
  workflow_dispatch:
    inputs:
      skip_sync:
        description: 'Skip content sync'
        type: boolean
        default: false
      deployment_target:
        description: 'Deployment target'
        type: choice
        options:
          - github-pages
          - vercel
        default: github-pages
      railway_service:
        description: 'Deploy sync service to Railway'
        type: boolean
        default: true
```

### 3. Emergency Rollback
```yaml
# For quick rollbacks
name: Emergency Rollback
on:
  workflow_dispatch:
    inputs:
      commit_sha:
        description: 'Commit SHA to rollback to'
        required: true
      rollback_railway:
        description: 'Rollback Railway service'
        type: boolean
        default: false

jobs:
  rollback:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout specific commit
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.commit_sha }}

      - name: Rollback Railway service
        if: github.event.inputs.rollback_railway == 'true'
        uses: railwayapp/railway-deploy@v1.1.0
        with:
          railway_token: ${{ secrets.RAILWAY_TOKEN }}
          service: sync-service
```

## Monitoring and Notifications

```yaml
- name: Notify on failure
  if: failure()
  uses: 8398a7/action-slack@v3
  with:
    status: failure
    text: |
      🚨 CMS Deployment Failed!
      - Sync Service: ${{ steps.deploy-sync.outcome }}
      - Static Site: ${{ steps.deploy-static.outcome }}
      - Commit: ${{ github.sha }}
  env:
    SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

- name: Notify on success
  if: success()
  uses: 8398a7/action-slack@v3
  with:
    status: success
    text: |
      ✅ CMS Deployment Successful!
      - Railway Service: Deployed
      - Static Site: Updated
      - Commit: ${{ github.sha }}
      - Railway URL: ${{ steps.railway-deploy.outputs.url }}
  env:
    SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

- name: Railway deployment status
  if: always()
  run: |
    echo "Railway deployment status: ${{ steps.railway-deploy.outcome }}"
    echo "Railway service URL: ${{ steps.railway-deploy.outputs.url }}"
```

## Testing Checklist

### Webhook Integration
- [ ] PocketBase webhooks configured correctly
- [ ] GitHub repository dispatch triggers working
- [ ] Webhook payload parsing functional
- [ ] Webhook signature verification working
- [ ] Test webhook triggers workflow successfully

### Content Sync
- [ ] Targeted sync works for webhook triggers
- [ ] Incremental sync detects changes correctly
- [ ] Full sync option works for manual triggers
- [ ] Content sync executes successfully
- [ ] Sync outputs proper change detection

### Build and Deploy
- [ ] Hugo build completes without errors
- [ ] Railway deployment succeeds
- [ ] Static site deployment works
- [ ] Conditional deployment based on changes

### Error Handling
- [ ] Webhook failures trigger polling fallback
- [ ] Error handling works properly
- [ ] Failed deployments trigger notifications
- [ ] Rollback procedures functional

### Monitoring
- [ ] Notifications are sent for success/failure
- [ ] Manual triggers function correctly
- [ ] Environment variables accessible
- [ ] Railway secrets properly configured
- [ ] Health checks pass on Railway service
- [ ] Workflow monitoring and logging working

## Performance Optimization
- Cache Node.js dependencies
- Cache Hugo resources
- Parallel job execution where possible
- Conditional job execution
- Artifact cleanup automation

## Security Considerations
- Secure secret management
- Limited workflow permissions
- Environment protection rules
- Audit logging enabled
- Access control for manual triggers

## Rollback Plan
- Disable automated workflows
- Revert Railway service to previous deployment
- Restore previous static site deployment
- Use Railway CLI for emergency rollbacks
- Document any issues encountered

### Railway Rollback Commands
```bash
# Rollback Railway service to previous deployment
railway rollback --service sync-service

# Check deployment history
railway logs --service sync-service

# Manual redeploy from specific commit
railway deploy --service sync-service --detach
```

## Notes
- Monitor workflow execution times
- Optimize for cost efficiency with Railway's usage-based pricing
- Plan for scaling requirements with Railway's auto-scaling
- Consider workflow complexity and Railway service dependencies
- Railway provides built-in monitoring and logging
- Use Railway's preview deployments for testing
- Consider Railway's database services for future expansion

## Progress Tracker
- [ ] **Planning**: Requirements analysis and approach definition
- [ ] **Setup**: Environment preparation and tool installation
- [ ] **Implementation**: Core functionality development
- [ ] **Testing**: Verification and validation
- [ ] **Documentation**: User guides and technical docs
- [ ] **Review**: Code review and quality assurance
- [ ] **Deployment**: Production deployment preparation
- [ ] **Complete**: Task fully implemented and verified

## Related Tasks
- **Previous**: Task 055 (User Session Management)
- **Next**: Task 057 (Railway Integration Setup)
- **Related**: Task 026-035 (Content Sync), Task 059 (Build Hook Implementation)

## Risk Assessment
- **Medium Risk**: CI/CD complexity and dependencies
- **Mitigation**: Incremental implementation, comprehensive testing, fallback procedures

---
*Task Created: [Current Date]*
*Last Updated: [Current Date]*
*Status: Not Started*
