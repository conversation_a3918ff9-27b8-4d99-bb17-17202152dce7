# Task 006: Site Settings Collection Schema





## Task Information
- **Task ID**: 006
- **Title**: Site Settings Collection Schema
- **Category**: Schema Design
- **Phase**: 1
- **Priority**: High
- **Estimated Complexity**: Medium
- **Dependencies**: Task 004 (Database Initialization), Task 005 (Basic Security Configuration)

## Description
Design and implement the Site Settings collection schema in PocketBase to manage global website configuration including company information, contact details, and social media links. This collection will store singleton data that affects the entire website.

## Detailed Requirements
1. Create Site Settings collection with appropriate fields
2. Define field types, validation rules, and constraints
3. Set up API access rules and permissions
4. Create default site settings record
5. Implement data validation and sanitization
6. Document schema structure and usage

## Technical Specifications
- **Collection Name**: `site_settings`
- **Collection Type**: Base collection
- **Record Limit**: Single record (singleton pattern)
- **API Access**: Admin and Editor roles
- **Validation**: Required fields and format validation

## Schema Definition
```json
{
  "name": "site_settings",
  "type": "base",
  "fields": [
    {
      "name": "company_name",
      "type": "text",
      "required": true,
      "max": 100
    },
    {
      "name": "company_tagline",
      "type": "text",
      "required": false,
      "max": 200
    },
    {
      "name": "company_description",
      "type": "editor",
      "required": false
    },
    {
      "name": "contact_email",
      "type": "email",
      "required": true
    },
    {
      "name": "contact_phone",
      "type": "text",
      "required": false,
      "max": 20
    },
    {
      "name": "contact_address",
      "type": "text",
      "required": false,
      "max": 500
    },
    {
      "name": "social_facebook",
      "type": "url",
      "required": false
    },
    {
      "name": "social_twitter",
      "type": "url",
      "required": false
    },
    {
      "name": "social_linkedin",
      "type": "url",
      "required": false
    },
    {
      "name": "social_instagram",
      "type": "url",
      "required": false
    },
    {
      "name": "logo",
      "type": "file",
      "required": false,
      "maxSelect": 1,
      "maxSize": 5242880
    },
    {
      "name": "favicon",
      "type": "file",
      "required": false,
      "maxSelect": 1,
      "maxSize": 1048576
    },
    {
      "name": "google_analytics_id",
      "type": "text",
      "required": false,
      "max": 50
    },
    {
      "name": "meta_title",
      "type": "text",
      "required": false,
      "max": 60
    },
    {
      "name": "meta_description",
      "type": "text",
      "required": false,
      "max": 160
    },
    {
      "name": "copyright_text",
      "type": "text",
      "required": false,
      "max": 200
    },
    {
      "name": "maintenance_mode",
      "type": "bool",
      "required": true,
      "default": false
    },
    {
      "name": "maintenance_message",
      "type": "text",
      "required": false,
      "max": 500
    }
  ]
}
```

## Acceptance Criteria
- [ ] Site Settings collection created in PocketBase
- [ ] All required fields properly configured
- [ ] Field validation rules implemented
- [ ] API access rules configured
- [ ] Default record created with sample data
- [ ] Schema documentation completed
- [ ] Collection tested via API

## Implementation Steps
1. **Collection Creation**
   - Create collection via PocketBase admin
   - Configure collection settings
   - Set collection type to "base"

2. **Field Configuration**
   - Add all required fields with proper types
   - Set validation rules and constraints
   - Configure file upload settings

3. **API Rules Setup**
   ```javascript
   // List/View Rule
   @request.auth.role != "" || @request.auth.role = "admin" || @request.auth.role = "editor"
   
   // Create Rule
   @request.auth.role != "" || @request.auth.role = "admin"
   
   // Update Rule
   @request.auth.role != "" || @request.auth.role = "admin" || @request.auth.role = "editor"
   
   // Delete Rule
   @request.auth.role != "" || @request.auth.role = "admin"
   ```

4. **Default Data Creation**
   ```json
   {
     "company_name": "PTBL",
     "company_tagline": "Your trusted technology partner",
     "contact_email": "<EMAIL>",
     "maintenance_mode": false,
     "copyright_text": "© 2024 PTBL. All rights reserved."
   }
   ```

## Files to Create/Modify
- `schemas/site_settings.json` (schema definition)
- `scripts/create-site-settings.js` (collection creation script)
- `data/default-site-settings.json` (default data)
- `docs/site-settings-schema.md` (documentation)

## Validation Rules
- **Email Format**: Valid email address format
- **URL Format**: Valid URL format for social media links
- **File Types**: 
  - Logo: PNG, JPG, SVG
  - Favicon: ICO, PNG
- **File Sizes**:
  - Logo: Max 5MB
  - Favicon: Max 1MB

## Testing Checklist
- [ ] Collection created successfully
- [ ] All fields accept valid data
- [ ] Validation rules reject invalid data
- [ ] File uploads work correctly
- [ ] API access rules enforced
- [ ] Default record created
- [ ] Schema matches documentation

## Usage Examples
```javascript
// Fetch site settings
const settings = await pb.collection('site_settings').getFirstListItem();

// Update company name
await pb.collection('site_settings').update(settings.id, {
  company_name: "New Company Name"
});

// Upload new logo
const formData = new FormData();
formData.append('logo', logoFile);
await pb.collection('site_settings').update(settings.id, formData);
```

## Rollback Plan
- Delete site_settings collection
- Remove any related API configurations
- Clean up uploaded files

## Notes
- Implement singleton pattern to ensure only one settings record
- Consider caching for frequently accessed settings
- Plan for future settings expansion
- Ensure proper backup of settings data

## Progress Tracker
- [ ] **Planning**: Requirements analysis and approach definition
- [ ] **Setup**: Environment preparation and tool installation
- [ ] **Implementation**: Core functionality development
- [ ] **Testing**: Verification and validation
- [ ] **Documentation**: User guides and technical docs
- [ ] **Review**: Code review and quality assurance
- [ ] **Deployment**: Production deployment preparation
- [ ] **Complete**: Task fully implemented and verified

## Related Tasks
- **Previous**: Task 005 (Basic Security Configuration)
- **Next**: Task 007 (Pages Collection Schema)
- **Related**: Task 021 (Site Settings Migration Script)

## Risk Assessment
- **Low Risk**: Standard collection creation
- **Mitigation**: Thorough testing and validation

---
*Task Created: [Current Date]*
*Last Updated: [Current Date]*
*Status: Not Started*
