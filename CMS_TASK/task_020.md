# Task 020: Existing Content Migration to PocketBase Collections

## Task Information
- **Task ID**: 020
- **Title**: Existing Content Migration to PocketBase Collections
- **Category**: Content Migration
- **Phase**: 2
- **Priority**: High
- **Estimated Complexity**: High
- **Dependencies**: Tasks 006-013 (All Collection Schemas), Task 004 (Database Initialization)

## Description
Migrate all existing content from the current Hugo site structure to the corresponding PocketBase collections. This includes extracting content from Hugo markdown files, TOML configuration files, and static assets, then transforming and importing them into the appropriate PocketBase collections while preserving data integrity and relationships.

## Detailed Requirements
1. Analyze and map existing Hugo content to PocketBase collections
2. Extract content from Hugo markdown files and frontmatter
3. Parse TOML configuration files for site settings and menus
4. Migrate static assets and establish proper file references
5. Transform multilingual content for English and French
6. Preserve content relationships and hierarchies
7. Validate migrated data integrity
8. Create rollback procedures for migration failures

## Technical Specifications
- **Source Format**: Hugo markdown files, TOML configs, static assets
- **Target Format**: PocketBase collections via API
- **Migration Tool**: Deno-based migration scripts
- **Data Validation**: Schema compliance and relationship integrity
- **Backup Strategy**: Full backup before migration
- **Rollback Support**: Automated rollback on failure

## Content Mapping Analysis

### 1. Site Settings (→ site_settings collection)
**Source**: `config/_default/params.toml`
```toml
# Current structure
favicon = "images/favicon.ico"
logo = "images/ptbl-logo.png"
description = "Power Telco Business Limited"
author = "Termite Tech Lab"
address = "2nd Floor, Omanye Aba building..."
mobile = "+233 24-388-9991"
email = "<EMAIL>"
```

**Target Mapping**:
- `logo` → `logo` (file upload)
- `favicon` → `favicon` (file upload)
- `description` → `company_description`
- `address` → `contact_address`
- `mobile` → `contact_phone`
- `email` → `contact_email`
- Social links → `social_*` fields

### 2. Pages (→ pages collection)
**Source**: `content/english/*.md`, `content/french/*.md`
```yaml
# Example: content/english/about.md
title: "About Us"
watermark: "About"
page_header_image: "images/background/about.jpg"
description: "Power Telco Business Limited..."
layout: "about"
```

**Target Mapping**:
- `title` → `title`
- `description` → `meta_description`
- `page_header_image` → `featured_image`
- `watermark` → `watermark`
- File content → `content`
- File path → `slug` generation

### 3. Navigation Menus (→ navigation_menus collection)
**Source**: `config/_default/menus.en.toml`, `config/_default/menus.fr.toml`
```toml
[[main]]
name = "About"
URL = "about"
weight = 2

[[footer]]
name = "About us"
URL = "about"
```

**Target Mapping**:
- `name` → `label`
- `URL` → `url`
- `weight` → `weight`
- Menu type (main/footer) → `menu_name`
- File language → `language`

### 4. Team Members (→ team_members collection)
**Source**: Extracted from `content/english/about.md` leadership section
```yaml
management:
  members:
    - name: "Leonardo Lamptey"
      position: "Chief Executive Officer"
      image: "images/team/leonardo.jpeg"
      bio: "Our CEO is a lawyer..."
```

**Target Mapping**:
- `name` → `name`
- `position` → `position`
- `image` → `photo`
- `bio` → `bio`

## Acceptance Criteria
- [ ] All existing Hugo content identified and mapped
- [ ] Site settings migrated from params.toml
- [ ] All pages migrated with proper frontmatter
- [ ] Navigation menus migrated for both languages
- [ ] Team member data extracted and migrated
- [ ] Static assets uploaded and referenced correctly
- [ ] Multilingual content properly separated
- [ ] Data validation passes for all collections
- [ ] Migration rollback procedures tested
- [ ] Migration documentation completed

## Implementation Steps

### 1. Content Analysis Script
```typescript
// scripts/analyze-existing-content.ts
import { walk } from "https://deno.land/std@0.208.0/fs/walk.ts";
import { parse as parseToml } from "https://deno.land/std@0.208.0/toml/mod.ts";
import { parse as parseYaml } from "https://deno.land/std@0.208.0/yaml/mod.ts";

interface ContentInventory {
  pages: { english: string[], french: string[] };
  configs: string[];
  assets: string[];
  menus: { english: string, french: string };
}

export async function analyzeExistingContent(): Promise<ContentInventory> {
  const inventory: ContentInventory = {
    pages: { english: [], french: [] },
    configs: [],
    assets: [],
    menus: { english: "", french: "" }
  };

  // Scan content directories
  for await (const entry of walk("content")) {
    if (entry.isFile && entry.path.endsWith(".md")) {
      if (entry.path.includes("/english/")) {
        inventory.pages.english.push(entry.path);
      } else if (entry.path.includes("/french/")) {
        inventory.pages.french.push(entry.path);
      }
    }
  }

  // Scan config files
  for await (const entry of walk("config")) {
    if (entry.isFile && entry.path.endsWith(".toml")) {
      inventory.configs.push(entry.path);
    }
  }

  // Scan static assets
  for await (const entry of walk("static")) {
    if (entry.isFile) {
      inventory.assets.push(entry.path);
    }
  }

  return inventory;
}
```

### 2. Site Settings Migration
```typescript
// scripts/migrate-site-settings.ts
import PocketBase from 'pocketbase';

export async function migrateSiteSettings(pb: PocketBase) {
  // Read params.toml
  const paramsContent = await Deno.readTextFile("config/_default/params.toml");
  const params = parseToml(paramsContent);

  // Extract social links
  const socialLinks = params.social || [];
  const socialData: Record<string, string> = {};
  
  socialLinks.forEach((social: any) => {
    if (social.title === 'linkedin') {
      socialData.social_linkedin = social.link;
    }
    // Add other social platforms as needed
  });

  // Create site settings record
  const siteSettings = {
    company_name: "Power Telco Business Limited",
    company_tagline: "Empowering connectivity through fiber network infrastructure",
    company_description: params.description || "",
    contact_email: params.email || "",
    contact_phone: params.mobile || "",
    contact_address: params.address || "",
    meta_title: params.description || "",
    meta_description: params.description || "",
    copyright_text: "© 2024 Power Telco Business Limited. All rights reserved.",
    maintenance_mode: false,
    ...socialData
  };

  // Upload logo and favicon
  if (params.logo) {
    const logoFile = await uploadAsset(pb, params.logo);
    siteSettings.logo = logoFile;
  }

  if (params.favicon) {
    const faviconFile = await uploadAsset(pb, params.favicon);
    siteSettings.favicon = faviconFile;
  }

  // Create record
  const result = await pb.collection('site_settings').create(siteSettings);
  console.log('Site settings migrated:', result.id);
  
  return result;
}

async function uploadAsset(pb: PocketBase, assetPath: string) {
  const fullPath = `static/${assetPath}`;
  const fileData = await Deno.readFile(fullPath);
  const fileName = assetPath.split('/').pop() || 'file';
  
  const formData = new FormData();
  formData.append('file', new Blob([fileData]), fileName);
  
  return formData;
}
```

### 3. Pages Migration
```typescript
// scripts/migrate-pages.ts
export async function migratePages(pb: PocketBase) {
  const languages = ['english', 'french'];
  
  for (const lang of languages) {
    const contentDir = `content/${lang}`;
    
    for await (const entry of walk(contentDir)) {
      if (entry.isFile && entry.path.endsWith('.md')) {
        await migratePage(pb, entry.path, lang);
      }
    }
  }
}

async function migratePage(pb: PocketBase, filePath: string, language: string) {
  const content = await Deno.readTextFile(filePath);
  const { frontmatter, body } = parseFrontmatter(content);
  
  // Generate slug from file path
  const slug = generateSlug(filePath, language);
  const pageId = generatePageId(slug);
  
  const pageData = {
    page_id: pageId,
    language: language === 'english' ? 'en' : 'fr',
    title: frontmatter.title || '',
    slug: slug,
    content: body,
    meta_title: frontmatter.title || '',
    meta_description: frontmatter.description || '',
    watermark: frontmatter.watermark || '',
    layout: frontmatter.layout || 'default',
    status: frontmatter.draft === true ? 'draft' : 'published',
    translation_status: 'published'
  };

  // Handle featured image
  if (frontmatter.page_header_image) {
    const imageFile = await uploadAsset(pb, frontmatter.page_header_image);
    pageData.featured_image = imageFile;
  }

  // Create page record
  const result = await pb.collection('pages').create(pageData);
  console.log(`Page migrated: ${pageData.title} (${language})`);
  
  return result;
}

function parseFrontmatter(content: string) {
  const frontmatterRegex = /^---\n([\s\S]*?)\n---\n([\s\S]*)$/;
  const match = content.match(frontmatterRegex);
  
  if (!match) {
    return { frontmatter: {}, body: content };
  }
  
  const frontmatter = parseYaml(match[1]);
  const body = match[2].trim();
  
  return { frontmatter, body };
}

function generateSlug(filePath: string, language: string): string {
  const fileName = filePath.split('/').pop()?.replace('.md', '') || '';
  return fileName === '_index' ? '' : fileName;
}

function generatePageId(slug: string): string {
  return slug || 'home';
}
```

### 4. Navigation Menus Migration
```typescript
// scripts/migrate-menus.ts
export async function migrateMenus(pb: PocketBase) {
  const menuFiles = [
    { file: 'config/_default/menus.en.toml', language: 'en' },
    { file: 'config/_default/menus.fr.toml', language: 'fr' }
  ];

  for (const { file, language } of menuFiles) {
    try {
      const menuContent = await Deno.readTextFile(file);
      const menuData = parseToml(menuContent);

      // Migrate main menu
      if (menuData.main) {
        await migrateMenuItems(pb, menuData.main, 'main', language);
      }

      // Migrate footer menu
      if (menuData.footer) {
        await migrateMenuItems(pb, menuData.footer, 'footer', language);
      }

    } catch (error) {
      console.warn(`Menu file not found: ${file}`);
    }
  }
}

async function migrateMenuItems(pb: PocketBase, items: any[], menuName: string, language: string) {
  for (const item of items) {
    const menuItem = {
      menu_name: menuName,
      label: item.name,
      url: item.URL.startsWith('/') ? item.URL : `/${item.URL}`,
      weight: item.weight || 0,
      language: language,
      active: true,
      target: '_self',
      menu_depth: 0
    };

    const result = await pb.collection('navigation_menus').create(menuItem);
    console.log(`Menu item migrated: ${menuItem.label} (${language})`);
  }
}
```

### 5. Team Members Migration
```typescript
// scripts/migrate-team-members.ts
export async function migrateTeamMembers(pb: PocketBase) {
  // Extract team data from about.md files
  const languages = ['english', 'french'];

  for (const lang of languages) {
    const aboutFile = `content/${lang}/about.md`;

    try {
      const content = await Deno.readTextFile(aboutFile);
      const { frontmatter } = parseFrontmatter(content);

      if (frontmatter.leadership?.management?.members) {
        await migrateManagementTeam(pb, frontmatter.leadership.management.members, lang);
      }

    } catch (error) {
      console.warn(`About file not found: ${aboutFile}`);
    }
  }
}

async function migrateManagementTeam(pb: PocketBase, members: any[], language: string) {
  for (const member of members) {
    const teamMember = {
      member_id: generateMemberId(member.name),
      language: language === 'english' ? 'en' : 'fr',
      name: member.name,
      position: member.position,
      bio: member.bio || '',
      department: 'Management',
      active: true,
      sort_order: 0,
      translation_status: 'published'
    };

    // Upload photo
    if (member.image) {
      const photoFile = await uploadAsset(pb, member.image);
      teamMember.photo = photoFile;
    }

    const result = await pb.collection('team_members').create(teamMember);
    console.log(`Team member migrated: ${teamMember.name} (${language})`);
  }
}

function generateMemberId(name: string): string {
  return name.toLowerCase()
    .replace(/[^a-z0-9\s]/g, '')
    .replace(/\s+/g, '-')
    .substring(0, 50);
}
```

### 6. Main Migration Script
```typescript
// scripts/migrate-all-content.ts
import PocketBase from 'pocketbase';

export async function migrateAllContent() {
  const pb = new PocketBase(Deno.env.get('POCKETBASE_URL'));

  // Authenticate
  await pb.admins.authWithPassword(
    Deno.env.get('POCKETBASE_EMAIL')!,
    Deno.env.get('POCKETBASE_PASSWORD')!
  );

  console.log('Starting content migration...');

  try {
    // 1. Analyze existing content
    console.log('📊 Analyzing existing content...');
    const inventory = await analyzeExistingContent();
    console.log('Content inventory:', inventory);

    // 2. Create backup
    console.log('💾 Creating backup...');
    await createBackup();

    // 3. Migrate site settings
    console.log('⚙️ Migrating site settings...');
    await migrateSiteSettings(pb);

    // 4. Migrate pages
    console.log('📄 Migrating pages...');
    await migratePages(pb);

    // 5. Migrate navigation menus
    console.log('🧭 Migrating navigation menus...');
    await migrateMenus(pb);

    // 6. Migrate team members
    console.log('👥 Migrating team members...');
    await migrateTeamMembers(pb);

    // 7. Validate migration
    console.log('✅ Validating migration...');
    await validateMigration(pb);

    console.log('🎉 Migration completed successfully!');

  } catch (error) {
    console.error('❌ Migration failed:', error);

    // Attempt rollback
    console.log('🔄 Attempting rollback...');
    await rollbackMigration();

    throw error;
  }
}

async function createBackup() {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupDir = `backups/migration-${timestamp}`;

  await Deno.mkdir(backupDir, { recursive: true });

  // Copy content files
  await copyDirectory('content', `${backupDir}/content`);
  await copyDirectory('config', `${backupDir}/config`);
  await copyDirectory('static', `${backupDir}/static`);

  console.log(`Backup created: ${backupDir}`);
}

async function validateMigration(pb: PocketBase) {
  const validations = [
    { collection: 'site_settings', expected: 1 },
    { collection: 'pages', expected: 'multiple' },
    { collection: 'navigation_menus', expected: 'multiple' },
    { collection: 'team_members', expected: 'multiple' }
  ];

  for (const validation of validations) {
    const records = await pb.collection(validation.collection).getFullList();

    if (validation.expected === 1 && records.length !== 1) {
      throw new Error(`Expected 1 ${validation.collection} record, got ${records.length}`);
    } else if (validation.expected === 'multiple' && records.length === 0) {
      throw new Error(`Expected multiple ${validation.collection} records, got 0`);
    }

    console.log(`✅ ${validation.collection}: ${records.length} records`);
  }
}

async function rollbackMigration() {
  // Implementation for rollback procedures
  console.log('Rollback procedures would be implemented here');
}

// Run migration if script is executed directly
if (import.meta.main) {
  await migrateAllContent();
}
```

## Files to Create/Modify
- `scripts/migrate-all-content.ts` (main migration script)
- `scripts/analyze-existing-content.ts` (content analysis)
- `scripts/migrate-site-settings.ts` (site settings migration)
- `scripts/migrate-pages.ts` (pages migration)
- `scripts/migrate-menus.ts` (navigation menus migration)
- `scripts/migrate-team-members.ts` (team members migration)
- `scripts/utils/file-helpers.ts` (file utilities)
- `scripts/utils/validation-helpers.ts` (validation utilities)
- `docs/migration-guide.md` (migration documentation)

## Testing Checklist
- [ ] Content analysis script identifies all existing content
- [ ] Site settings migration preserves all configuration
- [ ] Pages migration maintains content and metadata
- [ ] Menu migration preserves hierarchy and links
- [ ] Team member migration includes photos and bios
- [ ] Asset uploads work correctly
- [ ] Multilingual content properly separated
- [ ] Data validation passes for all records
- [ ] Migration can be rolled back successfully
- [ ] Performance acceptable for large content sets

## Rollback Plan
- Restore PocketBase database from pre-migration backup
- Verify original Hugo site functionality
- Document migration issues for resolution
- Preserve migration logs for debugging

## Notes
- Run migration in staging environment first
- Validate all content before production migration
- Consider migration in batches for large sites
- Monitor PocketBase storage usage during migration
- Test all migrated content in Hugo after sync

## Progress Tracker
- [ ] **Planning**: Requirements analysis and approach definition
- [ ] **Setup**: Environment preparation and tool installation
- [ ] **Implementation**: Core functionality development
- [ ] **Testing**: Verification and validation
- [ ] **Documentation**: User guides and technical docs
- [ ] **Review**: Code review and quality assurance
- [ ] **Deployment**: Production deployment preparation
- [ ] **Complete**: Task fully implemented and verified

## Related Tasks
- **Previous**: Task 019 (Migration Data Validation Rules)
- **Next**: Task 021 (Site Settings Migration Script)
- **Dependencies**: Tasks 006-013 (All Collection Schemas)
- **Related**: Task 026 (Content Sync Core Framework)

## Risk Assessment
- **High Risk**: Data loss during migration
- **Mitigation**: Comprehensive backup and rollback procedures, staging environment testing

---
*Task Created: [Current Date]*
*Last Updated: [Current Date]*
*Status: Not Started*
```
