# Task 013: Navigation Menus Collection Schema

## Task Information
- **Task ID**: 013
- **Title**: Navigation Menus Collection Schema
- **Category**: Schema Design
- **Phase**: 1
- **Priority**: High
- **Estimated Complexity**: High
- **Dependencies**: Task 004 (Database Initialization), Task 005 (Basic Security Configuration)

## Description
Design and implement the Navigation Menus collection schema in PocketBase to manage website navigation structures. This includes support for hierarchical menus, multilingual content, drag-and-drop ordering, and automatic Hugo TOML generation.

## Detailed Requirements
1. Create Navigation Menus collection with hierarchical support
2. Implement multilingual menu support
3. Design flexible menu item structure
4. Set up parent-child relationships for nested menus
5. Configure ordering and weight system
6. Implement menu status and visibility controls

## Technical Specifications
- **Collection Name**: `navigation_menus`
- **Collection Type**: Base collection
- **Hierarchical Support**: Parent-child relationships
- **Multilingual**: Language-specific menu items
- **Ordering**: Weight-based ordering system

## Schema Definition
```json
{
  "name": "navigation_menus",
  "type": "base",
  "fields": [
    {
      "name": "menu_name",
      "type": "select",
      "required": true,
      "values": ["main", "footer", "sidebar", "mobile"]
    },
    {
      "name": "label",
      "type": "text",
      "required": true,
      "max": 100
    },
    {
      "name": "url",
      "type": "text",
      "required": false,
      "max": 500
    },
    {
      "name": "page_reference",
      "type": "relation",
      "required": false,
      "collectionId": "pages",
      "maxSelect": 1
    },
    {
      "name": "external_url",
      "type": "url",
      "required": false
    },
    {
      "name": "parent_item",
      "type": "relation",
      "required": false,
      "collectionId": "navigation_menus",
      "maxSelect": 1
    },
    {
      "name": "weight",
      "type": "number",
      "required": true,
      "min": 0,
      "max": 9999
    },
    {
      "name": "language",
      "type": "select",
      "required": true,
      "values": ["en", "fr"]
    },
    {
      "name": "active",
      "type": "bool",
      "required": true,
      "default": true
    },
    {
      "name": "target",
      "type": "select",
      "required": false,
      "values": ["_self", "_blank", "_parent", "_top"]
    },
    {
      "name": "css_class",
      "type": "text",
      "required": false,
      "max": 100
    },
    {
      "name": "icon",
      "type": "text",
      "required": false,
      "max": 50
    },
    {
      "name": "description",
      "type": "text",
      "required": false,
      "max": 200
    },
    {
      "name": "visibility_rules",
      "type": "json",
      "required": false
    },
    {
      "name": "menu_depth",
      "type": "number",
      "required": true,
      "min": 0,
      "max": 3,
      "default": 0
    }
  ]
}
```

## Acceptance Criteria
- [ ] Navigation Menus collection created
- [ ] Hierarchical menu structure supported
- [ ] Multilingual menu items configured
- [ ] Weight-based ordering implemented
- [ ] Page reference relationships working
- [ ] Menu validation rules enforced
- [ ] API access rules configured
- [ ] Sample menu data created

## Implementation Steps
1. **Collection Creation**
   - Create navigation_menus collection
   - Configure all field types and constraints
   - Set up relational fields

2. **Hierarchical Structure**
   ```javascript
   // Parent-child relationship validation
   if (parent_item && parent_item.menu_depth >= 2) {
     throw new Error("Maximum menu depth exceeded");
   }
   ```

3. **API Rules Configuration**
   ```javascript
   // List/View Rule
   @request.auth.role = "admin" || @request.auth.role = "editor" || active = true
   
   // Create Rule
   @request.auth.role = "admin" || @request.auth.role = "editor"
   
   // Update Rule
   @request.auth.role = "admin" || @request.auth.role = "editor"
   
   // Delete Rule
   @request.auth.role = "admin"
   ```

4. **Menu Depth Calculation**
   ```javascript
   // Auto-calculate menu depth based on parent
   if (parent_item) {
     menu_depth = parent_item.menu_depth + 1;
   } else {
     menu_depth = 0;
   }
   ```

## Menu Structure Examples
### Main Menu (English)
```json
[
  {
    "menu_name": "main",
    "label": "Home",
    "url": "/",
    "weight": 10,
    "language": "en",
    "active": true,
    "menu_depth": 0
  },
  {
    "menu_name": "main",
    "label": "Services",
    "url": "/services",
    "weight": 20,
    "language": "en",
    "active": true,
    "menu_depth": 0
  },
  {
    "menu_name": "main",
    "label": "Web Development",
    "url": "/services/web-development",
    "parent_item": "services_item_id",
    "weight": 10,
    "language": "en",
    "active": true,
    "menu_depth": 1
  }
]
```

## Files to Create/Modify
- `schemas/navigation_menus.json`
- `scripts/create-navigation-schema.js`
- `data/sample-menu-data.json`
- `docs/navigation-schema.md`
- `utils/menu-helpers.js`

## Validation Rules
- **Menu Depth**: Maximum 3 levels deep
- **Parent Validation**: Parent must exist and be in same menu
- **Language Consistency**: Parent and child must have same language
- **Weight Uniqueness**: Unique weight per menu/language/parent combination
- **URL Validation**: Either url, page_reference, or external_url required

## Hugo Integration Preparation
```javascript
// Menu structure for Hugo TOML generation
const menuStructure = {
  main: {
    en: [...menuItems],
    fr: [...menuItems]
  },
  footer: {
    en: [...menuItems],
    fr: [...menuItems]
  }
};
```

## Testing Checklist
- [ ] Collection created with all fields
- [ ] Parent-child relationships work
- [ ] Menu depth calculation correct
- [ ] Language filtering works
- [ ] Weight ordering functions
- [ ] Validation rules enforced
- [ ] API access rules working
- [ ] Sample data loads correctly

## Advanced Features
- **Conditional Visibility**: Based on user roles or page context
- **Menu Caching**: For performance optimization
- **Breadcrumb Generation**: Automatic breadcrumb creation
- **Menu Analytics**: Track menu item clicks

## Rollback Plan
- Delete navigation_menus collection
- Remove any menu-related configurations
- Restore static menu configurations

## Notes
- Design for flexibility and future expansion
- Consider menu performance implications
- Plan for menu import/export functionality
- Ensure proper menu validation

## Progress Tracker
- [ ] **Planning**: Requirements analysis and approach definition
- [ ] **Setup**: Environment preparation and tool installation
- [ ] **Implementation**: Core functionality development
- [ ] **Testing**: Verification and validation
- [ ] **Documentation**: User guides and technical docs
- [ ] **Review**: Code review and quality assurance
- [ ] **Deployment**: Production deployment preparation
- [ ] **Complete**: Task fully implemented and verified

## Related Tasks
- **Previous**: Task 012 (Media Library Collection Schema)
- **Next**: Task 014 (User Roles & Permissions Setup)
- **Related**: Task 033 (Menu System Integration), Task 046-050 (Menu Management UI)

## Risk Assessment
- **Medium Risk**: Complex hierarchical structure
- **Mitigation**: Thorough testing of parent-child relationships

---
*Task Created: [Current Date]*
*Last Updated: [Current Date]*
*Status: Not Started*
