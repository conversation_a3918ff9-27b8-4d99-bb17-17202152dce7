# Task 071: Multilingual Content Schema

## Task Information
- **Task ID**: 071
- **Title**: Multilingual Content Schema
- **Category**: Internationalization (i18n)
- **Phase**: 5
- **Priority**: Medium
- **Estimated Complexity**: High
- **Dependencies**: Tasks 006-013 (All Collection Schemas)

## Description
Enhance the existing PocketBase collections to support multilingual content management for English and French languages. This includes modifying collection schemas, implementing language-specific content fields, and creating a robust system for managing translations across all content types.

## Detailed Requirements
1. Modify existing collections for multilingual support
2. Implement language-specific content fields
3. Create translation relationship management
4. Design language fallback mechanisms
5. Implement translation status tracking
6. Create language-aware API queries
7. Develop translation workflow tools

## Technical Specifications
- **Supported Languages**: English (en), French (fr)
- **Default Language**: English
- **Translation Strategy**: Separate records per language with relationships
- **Fallback Mechanism**: Default to English if translation missing
- **Status Tracking**: Translation completeness and approval status

## Multilingual Architecture
```
Multilingual Content Structure
├── Base Content (Language-neutral)
│   ├── ID, timestamps, status
│   ├── Media references
│   └── Structural data
├── Language-Specific Content
│   ├── Title, description, content
│   ├── SEO metadata
│   └── Language-specific URLs
└── Translation Management
    ├── Translation status
    ├── Translator assignments
    └── Approval workflow
```

## Schema Modifications

### 1. Enhanced Pages Collection
```json
{
  "name": "pages",
  "type": "base",
  "fields": [
    {
      "name": "page_id",
      "type": "text",
      "required": true,
      "unique": true
    },
    {
      "name": "language",
      "type": "select",
      "required": true,
      "values": ["en", "fr"]
    },
    {
      "name": "title",
      "type": "text",
      "required": true,
      "max": 200
    },
    {
      "name": "slug",
      "type": "text",
      "required": true,
      "max": 100
    },
    {
      "name": "content",
      "type": "editor",
      "required": false
    },
    {
      "name": "meta_title",
      "type": "text",
      "required": false,
      "max": 60
    },
    {
      "name": "meta_description",
      "type": "text",
      "required": false,
      "max": 160
    },
    {
      "name": "translation_source",
      "type": "relation",
      "required": false,
      "collectionId": "pages",
      "maxSelect": 1
    },
    {
      "name": "translation_status",
      "type": "select",
      "required": true,
      "values": ["draft", "in_progress", "review", "approved", "published"],
      "default": "draft"
    },
    {
      "name": "translator",
      "type": "relation",
      "required": false,
      "collectionId": "users",
      "maxSelect": 1
    },
    {
      "name": "translation_notes",
      "type": "text",
      "required": false,
      "max": 1000
    }
  ]
}
```

### 2. Enhanced Blog Posts Collection
```json
{
  "name": "blog_posts",
  "type": "base",
  "fields": [
    {
      "name": "post_id",
      "type": "text",
      "required": true,
      "unique": true
    },
    {
      "name": "language",
      "type": "select",
      "required": true,
      "values": ["en", "fr"]
    },
    {
      "name": "title",
      "type": "text",
      "required": true,
      "max": 200
    },
    {
      "name": "slug",
      "type": "text",
      "required": true,
      "max": 100
    },
    {
      "name": "excerpt",
      "type": "text",
      "required": false,
      "max": 300
    },
    {
      "name": "content",
      "type": "editor",
      "required": true
    },
    {
      "name": "translation_source",
      "type": "relation",
      "required": false,
      "collectionId": "blog_posts",
      "maxSelect": 1
    },
    {
      "name": "translation_status",
      "type": "select",
      "required": true,
      "values": ["draft", "in_progress", "review", "approved", "published"],
      "default": "draft"
    }
  ]
}
```

## Acceptance Criteria
- [ ] All collections support multilingual content
- [ ] Language-specific content fields implemented
- [ ] Translation relationship system working
- [ ] Translation status tracking functional
- [ ] Language fallback mechanism operational
- [ ] API queries support language filtering
- [ ] Translation workflow tools created
- [ ] Data migration scripts for existing content

## Implementation Steps
1. **Collection Schema Updates**
   ```javascript
   // Update existing collections
   const collections = ['pages', 'blog_posts', 'services', 'team_members'];
   
   for (const collection of collections) {
     await updateCollectionSchema(collection, multilingualFields);
   }
   ```

2. **Translation Relationship System**
   ```javascript
   class TranslationManager {
     async createTranslation(sourceId, targetLanguage, translatorId) {
       const sourceRecord = await this.getRecord(sourceId);
       const translationData = {
         ...sourceRecord,
         language: targetLanguage,
         translation_source: sourceId,
         translation_status: 'draft',
         translator: translatorId
       };
       
       return await this.createRecord(translationData);
     }
   
     async getTranslations(recordId) {
       return await this.pb.collection(this.collectionName).getFullList({
         filter: `translation_source="${recordId}"`
       });
     }
   }
   ```

3. **Language Fallback System**
   ```javascript
   class LanguageFallback {
     async getContent(contentId, language = 'en') {
       // Try to get content in requested language
       let content = await this.getByLanguage(contentId, language);
       
       // Fallback to default language if not found
       if (!content && language !== 'en') {
         content = await this.getByLanguage(contentId, 'en');
       }
       
       return content;
     }
   }
   ```

4. **Translation Status Tracking**
   ```javascript
   const translationWorkflow = {
     draft: {
       next: ['in_progress'],
       permissions: ['translator', 'admin']
     },
     in_progress: {
       next: ['review', 'draft'],
       permissions: ['translator', 'admin']
     },
     review: {
       next: ['approved', 'in_progress'],
       permissions: ['editor', 'admin']
     },
     approved: {
       next: ['published', 'review'],
       permissions: ['editor', 'admin']
     },
     published: {
       next: ['review'],
       permissions: ['admin']
     }
   };
   ```

## Files to Create/Modify
- `schemas/multilingual/pages-i18n.json`
- `schemas/multilingual/blog-posts-i18n.json`
- `schemas/multilingual/services-i18n.json`
- `scripts/migrate-to-multilingual.js`
- `src/utils/TranslationManager.js`
- `src/utils/LanguageFallback.js`
- `admin/js/translation-workflow.js`

## Translation Workflow
### 1. Content Creation
```javascript
// Create base content in default language
const baseContent = await createContent({
  title: "Original Title",
  content: "Original content...",
  language: "en",
  translation_status: "published"
});

// Create translation placeholder
const translation = await createTranslation(baseContent.id, "fr");
```

### 2. Translation Process
```javascript
// Assign translator
await assignTranslator(translation.id, translatorId);

// Update translation status
await updateTranslationStatus(translation.id, "in_progress");

// Submit for review
await updateTranslationStatus(translation.id, "review");

// Approve and publish
await updateTranslationStatus(translation.id, "approved");
await updateTranslationStatus(translation.id, "published");
```

## API Query Examples
```javascript
// Get all pages in French
const frenchPages = await pb.collection('pages').getFullList({
  filter: 'language="fr" && translation_status="published"'
});

// Get page with fallback
const page = await getPageWithFallback('about-us', 'fr');

// Get translation status overview
const translationStatus = await pb.collection('pages').getFullList({
  filter: 'translation_status!="published"',
  fields: 'id,title,language,translation_status,translator'
});
```

## Hugo Integration
```javascript
// Generate language-specific content files
const generateHugoContent = async (records) => {
  const groupedByLanguage = groupBy(records, 'language');
  
  for (const [language, content] of Object.entries(groupedByLanguage)) {
    await generateContentFiles(content, `content/${language}/`);
  }
};

// Hugo frontmatter with language support
const frontmatter = {
  title: record.title,
  slug: record.slug,
  language: record.language,
  translationKey: record.page_id,
  weight: record.weight
};
```

## Testing Checklist
- [ ] Schema updates applied successfully
- [ ] Translation relationships work correctly
- [ ] Language fallback functions properly
- [ ] Translation status workflow operational
- [ ] API queries filter by language
- [ ] Hugo content generation supports i18n
- [ ] Data migration completed successfully
- [ ] Translation tools functional

## Performance Considerations
- Index language fields for faster queries
- Cache frequently accessed translations
- Optimize fallback query performance
- Consider translation memory for efficiency

## Rollback Plan
- Revert schema changes
- Restore original collection structures
- Migrate multilingual data back to single language
- Update API queries to remove language filtering

## Notes
- Plan for future language additions
- Consider professional translation services integration
- Implement translation quality assurance
- Monitor translation workflow efficiency

## Progress Tracker
- [ ] **Planning**: Requirements analysis and approach definition
- [ ] **Setup**: Environment preparation and tool installation
- [ ] **Implementation**: Core functionality development
- [ ] **Testing**: Verification and validation
- [ ] **Documentation**: User guides and technical docs
- [ ] **Review**: Code review and quality assurance
- [ ] **Deployment**: Production deployment preparation
- [ ] **Complete**: Task fully implemented and verified

## Related Tasks
- **Previous**: Task 070 (Alert Configuration)
- **Next**: Task 072 (Language-Specific Content Fields)
- **Related**: Task 074 (Language Switcher UI), Task 075 (Multilingual Menu Support)

## Risk Assessment
- **High Risk**: Complex schema changes and data migration
- **Mitigation**: Thorough testing, backup procedures, incremental implementation

---
*Task Created: [Current Date]*
*Last Updated: [Current Date]*
*Status: Not Started*
