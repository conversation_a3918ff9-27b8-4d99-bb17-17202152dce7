{"name": "site_settings", "type": "base", "system": false, "schema": [{"id": "company_name", "name": "company_name", "type": "text", "required": true, "presentable": false, "unique": false, "options": {"min": null, "max": 100, "pattern": ""}}, {"id": "company_tagline", "name": "company_tagline", "type": "text", "required": false, "presentable": false, "unique": false, "options": {"min": null, "max": 200, "pattern": ""}}, {"id": "company_description", "name": "company_description", "type": "editor", "required": false, "presentable": false, "unique": false, "options": {"convertUrls": false}}, {"id": "contact_email", "name": "contact_email", "type": "email", "required": true, "presentable": false, "unique": false, "options": {"exceptDomains": null, "onlyDomains": null}}, {"id": "contact_phone", "name": "contact_phone", "type": "text", "required": false, "presentable": false, "unique": false, "options": {"min": null, "max": 20, "pattern": ""}}, {"id": "contact_address", "name": "contact_address", "type": "text", "required": false, "presentable": false, "unique": false, "options": {"min": null, "max": 500, "pattern": ""}}, {"id": "social_facebook", "name": "social_facebook", "type": "url", "required": false, "presentable": false, "unique": false, "options": {"exceptDomains": null, "onlyDomains": null}}, {"id": "social_twitter", "name": "social_twitter", "type": "url", "required": false, "presentable": false, "unique": false, "options": {"exceptDomains": null, "onlyDomains": null}}, {"id": "social_linkedin", "name": "social_linkedin", "type": "url", "required": false, "presentable": false, "unique": false, "options": {"exceptDomains": null, "onlyDomains": null}}, {"id": "social_instagram", "name": "social_instagram", "type": "url", "required": false, "presentable": false, "unique": false, "options": {"exceptDomains": null, "onlyDomains": null}}, {"id": "logo", "name": "logo", "type": "file", "required": false, "presentable": false, "unique": false, "options": {"mimeTypes": ["image/png", "image/jpeg", "image/svg+xml"], "thumbs": ["100x100"], "maxSelect": 1, "maxSize": 5242880, "protected": false}}, {"id": "favicon", "name": "favicon", "type": "file", "required": false, "presentable": false, "unique": false, "options": {"mimeTypes": ["image/x-icon", "image/png"], "thumbs": null, "maxSelect": 1, "maxSize": 1048576, "protected": false}}, {"id": "google_analytics_id", "name": "google_analytics_id", "type": "text", "required": false, "presentable": false, "unique": false, "options": {"min": null, "max": 50, "pattern": ""}}, {"id": "meta_title", "name": "meta_title", "type": "text", "required": false, "presentable": false, "unique": false, "options": {"min": null, "max": 60, "pattern": ""}}, {"id": "meta_description", "name": "meta_description", "type": "text", "required": false, "presentable": false, "unique": false, "options": {"min": null, "max": 160, "pattern": ""}}, {"id": "copyright_text", "name": "copyright_text", "type": "text", "required": false, "presentable": false, "unique": false, "options": {"min": null, "max": 200, "pattern": ""}}, {"id": "maintenance_mode", "name": "maintenance_mode", "type": "bool", "required": true, "presentable": false, "unique": false, "options": {}}, {"id": "maintenance_message", "name": "maintenance_message", "type": "text", "required": false, "presentable": false, "unique": false, "options": {"min": null, "max": 500, "pattern": ""}}], "indexes": [], "listRule": "@request.auth.id != \"\"", "viewRule": "@request.auth.id != \"\"", "createRule": "@request.auth.role = \"admin\"", "updateRule": "@request.auth.id != \"\" && (@request.auth.role = \"admin\" || @request.auth.role = \"editor\")", "deleteRule": "@request.auth.role = \"admin\"", "options": {}}