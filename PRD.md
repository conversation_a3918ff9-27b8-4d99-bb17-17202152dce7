# Product Requirements Document (PRD)
## PocketBase CMS Integration for PTBL Website

### 1. Overview

#### 1.1 Purpose
This document outlines the requirements for integrating PocketBase as a headless CMS for the PTBL corporate website, currently built with <PERSON>. The integration will enable content editors to manage website content through a user-friendly interface without requiring technical knowledge of <PERSON> or <PERSON><PERSON>.

#### 1.2 Project Goals
- Create a decoupled CMS architecture using PocketBase
- Enable non-technical staff to update website content
- Maintain the performance benefits of <PERSON>'s static site generation
- Implement a secure, reliable content management workflow

### 2. System Architecture

#### 2.1 Components
1. **PocketBase Server**: Backend CMS and database
2. **Hugo Static Site**: Frontend website
3. **Content Sync Script**: Middleware to transform PocketBase data to Hugo content
4. **Admin Interface**: Web UI for content management

#### 2.2 Deployment Architecture
```
[Content Editors] → [PocketBase Admin UI] → [PocketBase Database]
                                                     ↓
[Hugo Build Process] ← [Content Sync Script] ← [PocketBase API]
         ↓
[Static Website Files] → [Web Server/CDN] → [End Users]
```

### 3. Content Collections

#### 3.1 Core Collections
1. **Site Settings**
   - Company information
   - Contact details
   - Social media links

2. **Pages**
   - Title
   - Slug
   - Content sections
   - SEO metadata

3. **Blog Posts**
   - Title
   - Publication date
   - Author
   - Featured image
   - Content
   - Categories/Tags

4. **Services**
   - Title
   - Description
   - Icon
   - Order

5. **Team Members**
   - Name
   - Position
   - Photo
   - Bio
   - Social links

6. **Testimonials**
   - Client name
   - Company
   - Quote
   - Rating

7. **Media Library**
   - Images
   - Documents
   - Videos

8. **Navigation Menus**
   - Menu name (main, footer)
   - Menu items
     - Label
     - URL/Page reference
     - Parent item (for dropdowns)
     - Weight (order)
     - Language
     - Active status

### 4. Functional Requirements

#### 4.1 Content Management
- CRUD operations for all content types
- Rich text editing with image embedding
- Media library management
- Content preview before publishing
- Content versioning and rollback

#### 4.2 Multilingual Support
- Content management for English and French languages
- Language-specific content fields
- Translation status tracking

#### 4.3 User Management
- Role-based access control
- Admin, Editor, and Viewer roles
- User activity logging

#### 4.4 Content Deployment
- Automated content sync from PocketBase to Hugo
- Scheduled publishing
- Build hooks for Netlify deployment

#### 4.5 Menu Management
- Visual menu builder interface
- Drag-and-drop menu item ordering
- Support for nested menu items (dropdowns)
- Language-specific menu configurations
- Automatic generation of menu TOML files for Hugo

### 5. Technical Requirements

#### 5.1 PocketBase Setup
- PocketBase v0.28.3+ installation
- Custom collections schema
- API authentication and security
- Backup and restore functionality

#### 5.2 Content Sync Mechanism
- Node.js script to fetch PocketBase content
- Transformation of PocketBase records to Hugo markdown/data files
- Support for incremental updates
- Error handling and logging
- Menu structure conversion to Hugo-compatible format

#### 5.3 Hugo Integration
- Template modifications to use data from PocketBase
- Shortcodes for dynamic content
- Partial templates for reusable components
- Integration with Hugo's menu system

#### 5.4 Deployment Pipeline
- GitHub Actions workflow for automated builds
- Netlify integration for continuous deployment
- Environment-specific configurations

### 6. User Interface

#### 6.1 Admin Dashboard
- Overview of content status
- Quick access to recent edits
- Site performance metrics

#### 6.2 Content Editor
- WYSIWYG editing interface
- Markdown support
- Image upload and management
- SEO optimization tools

#### 6.3 Menu Editor
- Visual menu structure representation
- Drag-and-drop interface for menu organization
- Menu item properties editor
- Live preview of menu changes
- Language selector for multilingual menus

### 7. Security Requirements
- Secure authentication for admin access
- HTTPS for all communications
- Regular backups of PocketBase database
- Input validation and sanitization

### 8. Performance Requirements
- Admin interface response time < 2 seconds
- Content sync completion < 5 minutes
- Maintain current Hugo build time
- CDN integration for media assets

### 9. Implementation Phases

#### Phase 1: Foundation
- Set up PocketBase server
- Create basic collections schema
- Implement authentication

#### Phase 2: Content Migration
- Migrate existing content to PocketBase
- Create content sync script
- Update Hugo templates

#### Phase 3: Admin Interface
- Customize PocketBase admin UI
- Implement role-based permissions
- Add content preview functionality
- Develop menu management interface

#### Phase 4: Deployment & Testing
- Set up automated build pipeline
- Implement monitoring and error reporting
- User acceptance testing

### 10. Success Metrics
- Content update time reduced by 70%
- Zero technical support tickets for content updates
- 100% content accuracy between PocketBase and published site
- Website performance metrics maintained or improved
- Menu management time reduced by 80%

### 11. Future Considerations
- API for mobile applications
- Integration with marketing automation tools
- Advanced analytics dashboard
- Personalization features
