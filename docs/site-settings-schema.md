# Site Settings Collection Schema Documentation

## Overview
The `site_settings` collection stores global website configuration data including company information, contact details, social media links, and site-wide settings. This collection follows a singleton pattern, containing only one record that represents the current site configuration.

## Collection Details
- **Collection Name**: `site_settings`
- **Collection Type**: Base collection
- **Record Limit**: Single record (singleton pattern)
- **Created**: [Implementation Date]
- **Last Modified**: [Implementation Date]

## Schema Fields

### Company Information
| Field | Type | Required | Max Length | Description |
|-------|------|----------|------------|-------------|
| `company_name` | text | ✅ | 100 | Official company name |
| `company_tagline` | text | ❌ | 200 | Company tagline or slogan |
| `company_description` | editor | ❌ | - | Rich text company description |

### Contact Information
| Field | Type | Required | Max Length | Description |
|-------|------|----------|------------|-------------|
| `contact_email` | email | ✅ | - | Primary contact email address |
| `contact_phone` | text | ❌ | 20 | Primary contact phone number |
| `contact_address` | text | ❌ | 500 | Physical address |

### Social Media Links
| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `social_facebook` | url | ❌ | Facebook page URL |
| `social_twitter` | url | ❌ | Twitter profile URL |
| `social_linkedin` | url | ❌ | LinkedIn company page URL |
| `social_instagram` | url | ❌ | Instagram profile URL |

### Media Assets
| Field | Type | Required | Max Size | Allowed Types | Description |
|-------|------|----------|----------|---------------|-------------|
| `logo` | file | ❌ | 5MB | PNG, JPG, SVG | Company logo |
| `favicon` | file | ❌ | 1MB | ICO, PNG | Website favicon |

### SEO & Analytics
| Field | Type | Required | Max Length | Description |
|-------|------|----------|------------|-------------|
| `google_analytics_id` | text | ❌ | 50 | Google Analytics tracking ID |
| `meta_title` | text | ❌ | 60 | Default meta title for SEO |
| `meta_description` | text | ❌ | 160 | Default meta description for SEO |

### Site Management
| Field | Type | Required | Max Length | Description |
|-------|------|----------|------------|-------------|
| `copyright_text` | text | ❌ | 200 | Copyright notice text |
| `maintenance_mode` | bool | ✅ | - | Enable/disable maintenance mode |
| `maintenance_message` | text | ❌ | 500 | Message shown during maintenance |

## API Access Rules

### Authentication Requirements
- **List/View**: Authenticated users only (`@request.auth.id != ""`)
- **Create**: Admin role only (`@request.auth.role = "admin"`)
- **Update**: Admin or Editor roles (`@request.auth.role = "admin" || @request.auth.role = "editor"`)
- **Delete**: Admin role only (`@request.auth.role = "admin"`)

### Security Notes
- Unauthenticated users cannot access site settings
- Only administrators can create or delete settings records
- Editors can modify existing settings but cannot create/delete
- File uploads are not protected (publicly accessible)

## Validation Rules

### Text Fields
- `company_name`: Required, maximum 100 characters
- `company_tagline`: Optional, maximum 200 characters
- `contact_phone`: Optional, maximum 20 characters
- `contact_address`: Optional, maximum 500 characters
- `google_analytics_id`: Optional, maximum 50 characters
- `meta_title`: Optional, maximum 60 characters (SEO best practice)
- `meta_description`: Optional, maximum 160 characters (SEO best practice)
- `copyright_text`: Optional, maximum 200 characters
- `maintenance_message`: Optional, maximum 500 characters

### Email Fields
- `contact_email`: Required, must be valid email format

### URL Fields
- All social media fields: Optional, must be valid URL format when provided

### File Fields
- `logo`: Optional, PNG/JPG/SVG only, maximum 5MB
- `favicon`: Optional, ICO/PNG only, maximum 1MB

### Boolean Fields
- `maintenance_mode`: Required, defaults to false

## Usage Examples

### Fetch Site Settings
```javascript
// Get the site settings record
const settings = await pb.collection('site_settings').getFirstListItem();
console.log(settings.company_name);
```

### Update Company Information
```javascript
// Update company details
await pb.collection('site_settings').update(settings.id, {
  company_name: "New Company Name",
  company_tagline: "New tagline",
  contact_email: "<EMAIL>"
});
```

### Upload Logo
```javascript
// Upload new logo file
const formData = new FormData();
formData.append('logo', logoFile);
await pb.collection('site_settings').update(settings.id, formData);
```

### Toggle Maintenance Mode
```javascript
// Enable maintenance mode
await pb.collection('site_settings').update(settings.id, {
  maintenance_mode: true,
  maintenance_message: "Site is under maintenance. Please check back later."
});
```

## Default Data
The collection is initialized with the following default data:

```json
{
  "company_name": "Power Telco Business Limited",
  "company_tagline": "Empowering connectivity through fiber network infrastructure",
  "contact_email": "<EMAIL>",
  "contact_phone": "+233 24-388-9991",
  "contact_address": "2nd Floor, Omanye Aba building, opposite Ohene Djan Sports Stadium, Accra, Ghana",
  "social_linkedin": "https://www.linkedin.com/company/power-telco-business-limted/",
  "meta_title": "PTBL - Power Telco Business Limited",
  "meta_description": "Power Telco Business Limited (PTBL) - ECG's wholly owned subsidiary for fiber optic asset commercialization and technical services in Ghana.",
  "copyright_text": "© 2024 Power Telco Business Limited. All rights reserved.",
  "maintenance_mode": false
}
```

## Implementation Notes

### Singleton Pattern
This collection is designed to contain only one record. The application should:
1. Check for existing records before creating new ones
2. Always update the existing record rather than creating multiple records
3. Implement proper error handling for missing settings

### File Handling
- Logo files include thumbnail generation (100x100)
- Favicon files do not include thumbnails
- All uploaded files are publicly accessible
- Consider implementing file cleanup when replacing assets

### Performance Considerations
- Site settings are frequently accessed, consider caching
- Use `getFirstListItem()` for singleton access
- Monitor file storage usage for uploaded assets

### Future Enhancements
- Additional social media platforms
- Multiple logo variants (light/dark themes)
- Localization support for multilingual sites
- Advanced SEO settings (Open Graph, Twitter Cards)

## Troubleshooting

### Common Issues
1. **Multiple Records**: If multiple records exist, use the first one and clean up duplicates
2. **Missing Record**: Always check if a record exists before attempting updates
3. **File Upload Errors**: Verify file size and type constraints
4. **Permission Errors**: Ensure proper authentication and role assignment

### Maintenance
- Regular backup of settings data
- Monitor file storage usage
- Validate URLs periodically
- Review and update SEO metadata

---
*Documentation generated for Task 006: Site Settings Collection Schema*
*Last updated: [Current Date]*
